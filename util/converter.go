package util

import (
	"strconv"
	"strings"
)

func ParseInt64(s string) int64 {
	var n int64

	for _, ch := range []byte(s) {
		ch -= '0'
		if ch > 9 {
			return 0
		}
		n = n*10 + int64(ch)
	}

	return n
}

func ParseInt(s string) int {
	var n int

	for _, ch := range []byte(s) {
		ch -= '0'
		if ch > 9 {
			return 0
		}
		n = n*10 + int(ch)
	}

	return n
}

func ParseBool(str string) bool {
	switch str {
	case "true", "1", "t", "TRUE", "True":
		return true
	}
	return false
}

func TrimStringFromDot(s string) string {
	if idx := strings.Index(s, "."); idx != -1 {
		return s[:idx]
	}
	if idx := strings.Index(s, ","); idx != -1 {
		return s[:idx]
	}
	return s
}

func ReplaceQueryParams(namedQuery string, params map[string]any) (string, []any) {
	var (
		i    = 1
		args []any
	)

	for k, v := range params {
		if k != "" && strings.Contains(namedQuery, ":"+k) {
			namedQuery = strings.ReplaceAll(namedQuery, ":"+k, "$"+strconv.Itoa(i))
			args = append(args, v)
			i++
		}
	}

	return namedQuery, args
}

func TiyinToSoum(amount int) int {
	return amount / 100
}
