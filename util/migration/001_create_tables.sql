CREATE TABLE IF NOT EXISTS payments (
	id SERIAL PRIMARY KEY,
	user_id INT NOT NULL,
	user_type user_type NOT NULL,
	order_id INT,
    card_id INT,
    amount INT NOT NULL,
    reason INT NOT NULL,
    status SMALLINT NOT NULL,
    provider_id SMALLINT NOT NULL,
	created_at timestamp(0) DEFAULT NOW() NOT NULL,
	updated_at timestamp(0) DEFAULT NOW() NOT NULL,
    invoice varchar(255) NOT NULL UNIQUE,
	is_p2p BOOLEAN
);
CREATE INDEX IF NOT EXISTS payments_user_id_idx ON payments USING btree (user_id, user_type);
CREATE INDEX IF NOT EXISTS payments_order_id_idx ON payments USING btree (order_id);

CREATE TABLE IF NOT EXISTS cards (
	id SERIAL PRIMARY KEY,
	user_id INT NOT NULL,
	user_type user_type NOT NULL,
	number VARCHAR(16) NOT NULL,
	expire VARCHAR(5),
	brand VARCHAR(16),
	category VARCHAR(16),
    is_deleted BOOL DEFAULT FALSE NOT NULL,
	created_at timestamp(0) DEFAULT NOW() NOT NULL,
	updated_at timestamp(0) DEFAULT NOW() NOT NULL,
	full_number VARCHAR(255),
	payme_token VARCHAR(415) UNIQUE,
	payme_hash VARCHAR(50),
	atmos_token VARCHAR(255) UNIQUE,
	atmos_hash VARCHAR(150)
);
CREATE INDEX IF NOT EXISTS cards_user_id_idx ON cards USING btree (user_id, user_type);
CREATE INDEX IF NOT EXISTS cards_payme_hash_idx ON cards USING btree (payme_hash);
CREATE INDEX IF NOT EXISTS cards_atmos_hash_idx ON cards USING btree (atmos_hash);

CREATE TABLE IF NOT EXISTS debts (
	id SERIAL PRIMARY KEY,
	user_id INT NOT NULL,
	user_type user_type NOT NULL,
    order_id INT NOT NULL UNIQUE,
    amount INT NOT NULL,
    is_paid BOOL DEFAULT FALSE NOT NULL,
	created_at timestamp(0) DEFAULT NOW() NOT NULL,
	updated_at timestamp(0) DEFAULT NOW() NOT NULL,
	payme_hash VARCHAR(50),
	atmos_hash VARCHAR(150)
);
CREATE INDEX IF NOT EXISTS debts_user_id_idx ON debts USING btree (user_id, user_type);

CREATE TABLE IF NOT EXISTS payments_orders (
	id SERIAL PRIMARY KEY,
	corp_id INT,
	client_id INT,
	client_id INT NOT NULL,
	driver_id INT NOT NULL,
    client_payment_status SMALLINT,
    driver_payment_status SMALLINT
);

CREATE TABLE IF NOT EXISTS card_brands (
	id SERIAL PRIMARY KEY,
	brand VARCHAR(24) NOT NULL,
	category VARCHAR(24)
);

CREATE TABLE IF NOT EXISTS card_bins (
	bin VARCHAR(6) NOT NULL PRIMARY KEY,
	brand_id INT NOT NULL,
	CONSTRAINT card_bins_card_brands_fk FOREIGN KEY (brand_id) REFERENCES card_brands(id) ON DELETE CASCADE ON UPDATE CASCADE
);