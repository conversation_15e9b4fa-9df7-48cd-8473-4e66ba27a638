CREATE TABLE IF NOT EXISTS card_payment_attempts (
    id SERIAL PRIMARY KEY,
    atmos_hash VARCHAR(150) NOT NULL,
    attempt_count INT NOT NULL DEFAULT 0,
    last_attempt_at TIMESTAMP DEFAULT NOW() NOT NULL,
    last_reset_at TIMESTAMP DEFAULT NOW() NOT NULL,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

CREATE INDEX IF NOT EXISTS card_payment_attempts_atmos_hash_idx ON card_payment_attempts USING btree (atmos_hash);
