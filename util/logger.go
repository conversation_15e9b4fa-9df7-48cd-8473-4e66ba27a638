package util

import (
	"fmt"
	"os"
	"time"

	fiber "github.com/gofiber/fiber/v3"
)

func FiberLogger() fiber.Handler {
	return func(c fiber.Ctx) (err error) {
		defer func() {
			if r := recover(); r != nil {
				var ok bool
				if err, ok = r.(error); !ok {
					err = fmt.Errorf("%v", r)
				}
			}
		}()

		start := time.Now()

		req := c.Request()
		path := string(req.URI().PathOriginal())
		query := string(req.URI().QueryString())

		err = c.Next()

		latency := time.Since(start)

		resp := c.Response()
		code := resp.StatusCode()

		if query != "" {
			path += "?" + query
		}

		if code != fiber.StatusOK {
			path += "    " + string(resp.Body())
		}

		msg := fmt.Sprintf("[FIBER]  | %3d | %13v | %-7s %s\n",
			code,
			latency,
			c.Method(),
			path,
		)

		fmt.Fprint(os.Stdout, msg)

		return
	}
}
