package util

import (
	"time"

	"github.com/riverqueue/river/rivertype"
)

type RiverCustomRetryPolicy struct {
}

func (RiverCustomRetryPolicy) NextRetry(job *rivertype.JobRow) time.Time {
	attempt := len(job.Errors)
	var duration time.Duration

	if attempt < 14 {
		switch attempt {
		case 0:
			duration = 2 * time.Second
		case 1:
			duration = 10 * time.Second
		case 2:
			duration = 30 * time.Second
		case 3:
			duration = 60 * time.Second
		case 4:
			duration = 120 * time.Second
		case 5:
			duration = 220 * time.Second
		case 6:
			duration = 350 * time.Second
		case 7:
			duration = 500 * time.Second
		case 8:
			duration = 700 * time.Second
		case 9:
			duration = 1000 * time.Second
		case 10:
			duration = 1350 * time.Second
		case 11:
			duration = 1800 * time.Second
		case 12:
			duration = 2300 * time.Second
		case 13:
			duration = 2900 * time.Second
		default:
			duration = 3600 * time.Second
		}
	} else {
		duration = 3600 * time.Second
	}

	return time.Now().UTC().Add(duration)
}
