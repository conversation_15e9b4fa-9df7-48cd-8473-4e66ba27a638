package redis

import (
	"context"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

var (
	client *redis.Client
	once   sync.Once
)

func Get(host, pass string, db int) *redis.Client {
	once.Do(func() {
		client = redis.NewClient(&redis.Options{
			Addr:     host,
			Password: pass,
			DB:       db,
		})

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		err := client.Ping(ctx).Err()
		if err != nil {
			panic(err)
		}
	})

	return client
}
