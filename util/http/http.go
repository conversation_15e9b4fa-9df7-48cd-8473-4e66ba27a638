package http

import (
	"bytes"
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/bytedance/sonic"
)

var json = sonic.ConfigDefault

const requestTimeout = 10 * time.Second

type errorResponse struct {
	Error struct {
		Type    string `json:"type"`
		Message string `json:"message"`
	} `json:"error"`
}

type Client struct {
	client *http.Client
}

func New() *Client {
	return &Client{
		client: &http.Client{
			Timeout: requestTimeout,
		},
	}
}

func (r *Client) Request(ctx context.Context, method, url string, request, response any) (errType string, err error) {
	reqBody, err := json.Marshal(request)
	if err != nil {
		return
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewReader(reqBody))
	if err != nil {
		return
	}

	req.Header = http.Header{
		"Content-Type": {"application/json"},
	}

	resp, err := r.client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errResp errorResponse
		_ = json.NewDecoder(resp.Body).Decode(&errResp)
		if errResp.Error.Type != "" {
			errType = errResp.Error.Type
			err = errors.New(errResp.Error.Message)
		} else {
			err = errors.New("response not ok: " + resp.Status)
		}
		return
	}

	err = json.NewDecoder(resp.Body).Decode(&response)

	return
}

func (r *Client) RequestWithoutBody(ctx context.Context, method, url string, response any) (errType string, err error) {
	req, err := http.NewRequestWithContext(ctx, method, url, nil)
	if err != nil {
		return
	}

	req.Header = http.Header{
		"Content-Type": {"application/json"},
	}

	resp, err := r.client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errResp errorResponse
		_ = json.NewDecoder(resp.Body).Decode(&errResp)
		if errResp.Error.Type != "" {
			errType = errResp.Error.Type
			err = errors.New(errResp.Error.Message)
		} else {
			err = errors.New("response not ok: " + resp.Status)
		}
		return
	}

	err = json.NewDecoder(resp.Body).Decode(&response)

	return
}

func (r *Client) RequestWithoutResponse(ctx context.Context, method, url string, request any) (errType string, err error) {
	reqBody, err := json.Marshal(request)
	if err != nil {
		return
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewReader(reqBody))
	if err != nil {
		return
	}

	req.Header = http.Header{
		"Content-Type": {"application/json"},
	}

	resp, err := r.client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errResp errorResponse
		_ = json.NewDecoder(resp.Body).Decode(&errResp)
		if errResp.Error.Type != "" {
			errType = errResp.Error.Type
			err = errors.New(errResp.Error.Message)
		} else {
			err = errors.New("response not ok: " + resp.Status)
		}
		return
	}

	return
}

func (r *Client) RequestWithoutBodyAndResponse(ctx context.Context, method, url string) (errType string, err error) {
	req, err := http.NewRequestWithContext(ctx, method, url, nil)
	if err != nil {
		return
	}

	req.Header = http.Header{
		"Content-Type": {"application/json"},
	}

	resp, err := r.client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errResp errorResponse
		_ = json.NewDecoder(resp.Body).Decode(&errResp)
		if errResp.Error.Type != "" {
			errType = errResp.Error.Type
			err = errors.New(errResp.Error.Message)
		} else {
			err = errors.New("response not ok: " + resp.Status)
		}
		return
	}

	return
}
