package fernet

import "testing"

var decodeKeys = []struct {
	enc string
	key Key
}{
	{
		"cw_0x689RpI-jtRR7oE8h_eQsKImvJapLeSbXpwF4e4=",
		Key{
			0x73, 0x0f, 0xf4, 0xc7, 0xaf, 0x3d, 0x46, 0x92,
			0x3e, 0x8e, 0xd4, 0x51, 0xee, 0x81, 0x3c, 0x87,
			0xf7, 0x90, 0xb0, 0xa2, 0x26, 0xbc, 0x96, 0xa9,
			0x2d, 0xe4, 0x9b, 0x5e, 0x9c, 0x05, 0xe1, 0xee,
		},
	},
	{
		"cw/0x689RpI+jtRR7oE8h/eQsKImvJapLeSbXpwF4e4=",
		Key{
			0x73, 0x0f, 0xf4, 0xc7, 0xaf, 0x3d, 0x46, 0x92,
			0x3e, 0x8e, 0xd4, 0x51, 0xee, 0x81, 0x3c, 0x87,
			0xf7, 0x90, 0xb0, 0xa2, 0x26, 0xbc, 0x96, 0xa9,
			0x2d, 0xe4, 0x9b, 0x5e, 0x9c, 0x05, 0xe1, 0xee,
		},
	},
	{
		"730ff4c7af3d46923e8ed451ee813c87f790b0a226bc96a92de49b5e9c05e1ee",
		Key{
			0x73, 0x0f, 0xf4, 0xc7, 0xaf, 0x3d, 0x46, 0x92,
			0x3e, 0x8e, 0xd4, 0x51, 0xee, 0x81, 0x3c, 0x87,
			0xf7, 0x90, 0xb0, 0xa2, 0x26, 0xbc, 0x96, 0xa9,
			0x2d, 0xe4, 0x9b, 0x5e, 0x9c, 0x05, 0xe1, 0xee,
		},
	},
	{
		"vahSjfa-8fkUMRb2E2DA-M1Cm0wCj9DHYQQRSL5TNCU=",
		Key{
			0xbd, 0xa8, 0x52, 0x8d, 0xf6, 0xbe, 0xf1, 0xf9,
			0x14, 0x31, 0x16, 0xf6, 0x13, 0x60, 0xc0, 0xf8,
			0xcd, 0x42, 0x9b, 0x4c, 0x02, 0x8f, 0xd0, 0xc7,
			0x61, 0x04, 0x11, 0x48, 0xbe, 0x53, 0x34, 0x25,
		},
	},
	{
		"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=",
		Key{},
	},
}

var encodeKeys = []struct {
	enc string
	key Key
}{
	{
		"cw_0x689RpI-jtRR7oE8h_eQsKImvJapLeSbXpwF4e4=",
		Key{
			0x73, 0x0f, 0xf4, 0xc7, 0xaf, 0x3d, 0x46, 0x92,
			0x3e, 0x8e, 0xd4, 0x51, 0xee, 0x81, 0x3c, 0x87,
			0xf7, 0x90, 0xb0, 0xa2, 0x26, 0xbc, 0x96, 0xa9,
			0x2d, 0xe4, 0x9b, 0x5e, 0x9c, 0x05, 0xe1, 0xee,
		},
	},
	{
		"vahSjfa-8fkUMRb2E2DA-M1Cm0wCj9DHYQQRSL5TNCU=",
		Key{
			0xbd, 0xa8, 0x52, 0x8d, 0xf6, 0xbe, 0xf1, 0xf9,
			0x14, 0x31, 0x16, 0xf6, 0x13, 0x60, 0xc0, 0xf8,
			0xcd, 0x42, 0x9b, 0x4c, 0x02, 0x8f, 0xd0, 0xc7,
			0x61, 0x04, 0x11, 0x48, 0xbe, 0x53, 0x34, 0x25,
		},
	},
	{
		"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=",
		Key{},
	},
}

func TestDecodeKey(t *testing.T) {
	for _, w := range decodeKeys {
		if g, err := DecodeKey(w.enc); err != nil {
			t.Fatal(err)
		} else if *g != w.key {
			t.Fatalf("expected %q, got %q", w.key, *g)
		}
	}
}

func TestDecodeKeys(t *testing.T) {
	keys, err := DecodeKeys()
	if err == nil {
		t.Fatal("expected err, got nil")
	}
	if keys != nil {
		t.Fatalf("expected nil keys, got %#v", keys)
	}
}

func TestEncode(t *testing.T) {
	for _, w := range encodeKeys {
		g := w.key.Encode()
		if g != w.enc {
			t.Fatalf("expected %q, got %q", w.enc, g)
		}
	}
}
