package payme

import (
	"errors"
)

const (
	InvalidFieldValueCode            = -31610
	TooLessAmoutCode                 = -31611
	TooBigAmountCode                 = -31612
	InvalidParamsCode                = -32602
	P2PIndenticalCardsCode           = -31630
	CardNotFoundCode                 = -31400
	CardNumberNotFoundCode           = -31300
	CardExpiredCode                  = -31301
	InvalidTokenFormatCode           = -32500
	InsufficientBalanceCode          = -31303
	ReceiptNotFoundCode              = -31602
	AccessDeniedCode                 = -32504
	MerchantNotFoundCode             = -31601
	MerchantUnavailableCode          = -31622
	MerchantErrorCode                = -31623
	PaycomServiceUnavailableCode     = -31001
	ProcessingCenterUnavailableCode  = -31002
	ProcessingCenterUnavailable2Code = -31624
	CommissionServiceUnavailableCode = -31626
	CheckAlreadyCancelled            = -31800
)

var (
	ErrUnknown                     = errors.New("unknown error")
	ErrTimeoutExceeded             = errors.New("timeout exceeded")
	ErrProcessingCenterUnavailable = errors.New("processing center unavailable")
	ErrCardExpired                 = errors.New("card expired")
	ErrCardNotFound                = errors.New("card not found")
	ErrInvalidTokenFormat          = errors.New("invalid token format")
	ErrInvalidParams               = errors.New("invalid params")
	ErrInvalidFieldValue           = errors.New("invalid field value")
	ErrTooLessAmount               = errors.New("too less amount")
	ErrTooBigAmount                = errors.New("too big amount")
	ErrP2PIdenticalCards           = errors.New("similar cards cannot be used for p2p processing")
	ErrInsufficientBalance         = errors.New("insufficient balance")
	ErrReceiptsNotFound            = errors.New("receipts not found")
	ErrReceiptsAlreadyPayed        = errors.New("receipts already payed")
	ErrMerchantNotFound            = errors.New("merchant not found")
	ErrMerchantUnavailable         = errors.New("merchant unavailable")
	ErrMerchantError               = errors.New("merchant error")
	ErrAccessDenied                = errors.New("access denied")
	ErrCheckAlreadyCancelled       = errors.New("check already cancalled")
	// ErrPaycomServiceUnavailable     = errors.New("paycom service unvailable")
	// ErrCommissionServiceUnavailable = errors.New("commission service unavailable")
)

func extractError(e *Error) error {
	switch e.Code {
	case InsufficientBalanceCode:
		return ErrInsufficientBalance

	case PaycomServiceUnavailableCode, ProcessingCenterUnavailableCode, CommissionServiceUnavailableCode, ProcessingCenterUnavailable2Code:
		return ErrProcessingCenterUnavailable

	case CardNotFoundCode, CardNumberNotFoundCode:
		return ErrCardNotFound

	case CardExpiredCode:
		return ErrCardExpired

	case InvalidTokenFormatCode:
		return ErrInvalidTokenFormat

	case P2PIndenticalCardsCode:
		if e.Data != nil {
			switch v := e.Data.(type) {
			case string:
				if v == "p2p_identical_cards" {
					return ErrP2PIdenticalCards
				}
			}
		}
		return ErrInsufficientBalance

	case InvalidParamsCode:
		return ErrInvalidParams

	case InvalidFieldValueCode:
		return ErrInvalidFieldValue

	case TooBigAmountCode:
		return ErrTooBigAmount

	case TooLessAmoutCode:
		return ErrTooLessAmount

	case ReceiptNotFoundCode:
		// if e.Origin == "receipt.pay_by_card" {
		// 	return ErrReceiptsAlreadyPayed
		// }
		return ErrReceiptsNotFound

	case MerchantUnavailableCode:
		return ErrMerchantUnavailable

	case MerchantNotFoundCode:
		return ErrMerchantNotFound

	case MerchantErrorCode:
		return ErrMerchantError

	case AccessDeniedCode:
		return ErrAccessDenied

	case CheckAlreadyCancelled:
		return ErrCheckAlreadyCancelled

	default:
		// if e.Code != 0 {
		// 	return fmt.Errorf("unknown error with code: %d", e.Code)
		// }
		return ErrUnknown
	}
}
