package payme

import null "github.com/guregu/null/v6"

const (
	CheckStatusCreated                  = 0  // Чек создан. Ожидание подтверждения оплаты
	CheckStatusTransactionCreation      = 1  // Первая стадия проверок. Создание транзакции в биллинге поставщика
	CheckStatusWithdrawing              = 2  // Списание денег с карты
	CheckStatusTransactionClosing       = 3  // Закрытие транзакции в биллинге поставщика
	CheckStatusPaid                     = 4  // Чек оплачен
	CheckStatusHolded                   = 5  // Чек захолдирован
	CheckStatusHold                     = 6  // Запрос на холдирование средств
	CheckStatusPaused                   = 20 // Чек стоит на паузе для ручного вмешательства
	CheckStatusCancellationQueued       = 21 // Чек в очереди на отмену
	CheckStatusTransactionClosingQueued = 30 // Чек в очереди на закрытие транзакции в биллинге поставщика
	CheckStatusCancelled                = 50 // Чек отменен
)

// request models

type PaymentDetails struct {
	OrderId  int
	CardId   int
	ReasonId int
	Amount   int
	Token    string
	Comment  string
}

type Request struct {
	Id     int64  `json:"id,omitempty"`
	Method string `json:"method"`
	Params Params `json:"params"`
}

type Account struct {
	DriverId      int    `json:"driver_id,omitempty"`
	OrderId       int    `json:"order_id,omitempty"`
	CardId        int    `json:"card_id,omitempty"`
	PaymentMethod string `json:"payment_method,omitempty"`
}

type Params struct {
	Amount    int      `json:"amount,omitempty"`
	Account   *Account `json:"account,omitempty"`
	ReceiptId string   `json:"id,omitempty"`
	Token     string   `json:"token,omitempty"`
	Comment   string   `json:"description,omitempty"`
}

// response models

type Response struct {
	// Jsonrpc string `json:"jsonrpc"`
	// Id     int `json:"id"` // request id
	Result struct {
		Success bool     `json:"success"` // card balance success
		State   null.Int `json:"state"`   // receipts state
		Receipt Receipt  `json:"receipt"`
		Card    Card     `json:"card"`
	} `json:"result"`
	Error *Error `json:"error"`
}

type Receipt struct {
	ID    string   `json:"_id"`
	State null.Int `json:"state"`
}

type Card struct {
	Number     string `json:"number"`
	Expire     string `json:"expire"`
	Token      string `json:"token"`
	Recurrent  bool   `json:"recurrent"`
	Verify     bool   `json:"verify"`
	Type       string `json:"type"`
	NumberHash string `json:"number_hash"`
}

type Error struct {
	Message string `json:"message"`
	Code    int    `json:"code"`
	Data    any    `json:"data"`
	// Origin string `json:"origin"`
}
