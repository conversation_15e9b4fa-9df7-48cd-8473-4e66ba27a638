package payme

import (
	"context"
)

func (c *Client) CardCheck(ctx context.Context, token string) (resp Response, err error) {
	req := Request{
		Method: "cards.check",
		Params: Params{
			Token: token,
		},
	}
	resp, err = c.sendRequest(ctx, req, true)
	return
}

func (c *Client) CardCheckLong(ctx context.Context, token string) (resp Response, err error) {
	req := Request{
		Method: "cards.check",
		Params: Params{
			Token: token,
		},
	}
	resp, err = c.sendRequestLong(ctx, req, true)
	return
}

func (c *Client) CardRemove(ctx context.Context, token string) (success bool, err error) {
	req := Request{
		Method: "cards.remove",
		Params: Params{
			Token: token,
		},
	}
	resp, err := c.sendRequest(ctx, req, true)
	success = resp.Result.Success
	return
}

func (c *Client) CardBalance(ctx context.Context, token string, amount int) (resp bool, err error) {
	req := Request{
		Method: "cards.checkBalance",
		Params: Params{
			Amount: SoumToTiyin(amount),
			Token:  token,
		},
	}
	result, err := c.sendRequest(ctx, req, true)
	resp = result.Result.Success
	return
}

func (c *Client) CardBalanceLong(ctx context.Context, token string, amount int) (resp bool, err error) {
	req := Request{
		Method: "cards.checkBalance",
		Params: Params{
			Amount: SoumToTiyin(amount),
			Token:  token,
		},
	}
	result, err := c.sendRequestLong(ctx, req, true)
	resp = result.Result.Success
	return
}
