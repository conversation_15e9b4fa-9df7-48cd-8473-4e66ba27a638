package payme

import "math"

func SoumToTiyin(amount int) int {
	return amount * 100
}

func TiyinToSoum(amount int) int {
	return amount / 100
}

// При P2P делаем скидку, так чтобы при снятии денег в Payme снималось сумма amount.
// К примеру: сумма заказа 7000 сум. При выполнении перевода к сумме заказа добавляется
// коммисия 1 %, итого c клиента снимется 7070 сум. Поэому в Payme отправляем 7000 / 1.01 сум, тогда
// сумма, которая снимется с клиента вместе с коммисей будет 7000 сум.
func GetPaymeP2PPriceWithoutCommissionInTiyin(amount int) int {
	return int(math.Round((float64(amount) / 1.01) * 100))
}

// func GetPaymeP2PPriceWithoutCommissionInTiyin2(amount int) int {
// 	return int(math.Round(float64(amount*100) / 1.01))
// }

// Метод для компенсации водителя для оплаты по P2P на 1%
func GetPaymeCommissionPrice(amount int) int {
	return amount - int(math.Round(float64(amount)/1.01))
}
