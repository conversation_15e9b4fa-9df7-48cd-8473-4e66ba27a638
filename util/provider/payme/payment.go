package payme

import (
	"context"
	"time"

	null "github.com/guregu/null/v6"
)

// create merchant check
func (c *Client) CreateReceipt(ctx context.Context, userId, orderId, cardId, amount int, paymentMethod, comment string) (receiptId string, err error) {
	req := Request{
		Id:     time.Now().UnixMilli(),
		Method: "receipts.create",
		Params: Params{
			Amount: SoumToTiyin(amount),
			Account: &Account{
				DriverId:      userId,
				OrderId:       orderId,
				CardId:        cardId,
				PaymentMethod: paymentMethod,
			},
			Comment: comment,
		},
	}
	result, err := c.sendRequestLong(ctx, req, true)
	receiptId = result.Result.Receipt.ID
	return
}

// create p2p check
func (c *Client) CreateReceiptP2P(ctx context.Context, orderId, amount int, token, comment string) (receiptId string, err error) {
	req := Request{
		Id:     time.Now().UnixMilli(),
		Method: "receipts.p2p",
		Params: Params{
			Amount: GetPaymeP2PPriceWithoutCommissionInTiyin(amount),
			Account: &Account{
				OrderId: orderId,
			},
			Token:   token,
			Comment: comment,
		},
	}
	result, err := c.sendRequestLong(ctx, req, true)
	receiptId = result.Result.Receipt.ID
	return
}

// pay check
func (c *Client) PayReceipt(ctx context.Context, token, receiptId string) (state null.Int, err error) {
	req := Request{
		Id:     time.Now().UnixMilli(),
		Method: "receipts.pay",
		Params: Params{
			Token:     token,
			ReceiptId: receiptId,
		},
	}
	resp, err := c.sendRequestLong(ctx, req, false)
	state = resp.Result.Receipt.State
	return
}

func (c *Client) GetReceiptStatus(ctx context.Context, receiptId string) (state null.Int, err error) {
	req := Request{
		Method: "receipts.check",
		Params: Params{
			ReceiptId: receiptId,
		},
	}
	result, err := c.sendRequestLong(ctx, req, false)
	state = result.Result.State
	return
}

func (c *Client) CancelReceipt(ctx context.Context, receiptId string) (state null.Int, err error) {
	req := Request{
		Method: "receipts.cancel",
		Params: Params{
			ReceiptId: receiptId,
		},
	}
	result, err := c.sendRequest(ctx, req, false)
	state = result.Result.Receipt.State
	return
}
