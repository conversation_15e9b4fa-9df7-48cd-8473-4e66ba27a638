package click

const (
	ClickPrepareAction  = 0
	ClickCompleteAction = 1
)

type WebhookRequest struct {
	Action            int    `json:"action"`
	ClickTransID      string `json:"click_trans_id"`
	ServiceID         string `json:"service_id"`
	ClickPaydocID     string `json:"click_paydoc_id"`
	MerchantTransID   string `json:"merchant_trans_id"`
	Amount            string `json:"amount"`
	SignTime          string `json:"sign_time"`
	Error             string `json:"error"`
	ErrorNote         string `json:"error_note"`
	MerchantPrepareID string `json:"merchant_prepare_id,omitempty"` // Only for complete action
	SignString        string `json:"sign_string"`
}

type WebhookResponse struct {
	Error             int    `json:"error"`
	ClickTransID      int    `json:"click_trans_id,omitempty"`
	MerchantPrepareID int    `json:"merchant_prepare_id,omitempty"`
	MerchantTransID   string `json:"merchant_trans_id,omitempty"`
	ErrorNote         string `json:"error_note,omitempty"`
}

func (r *WebhookResponse) SetTransactionNotFoundError(message string) {
	r.Error = -6
	r.ErrorNote = message
}

func (r *WebhookResponse) SetInternalError(message string) {
	r.Error = 500
	r.ErrorNote = message
}

func (r *WebhookResponse) SetIncorrectAmountError(message string) {
	r.Error = -2
	r.ErrorNote = message
}

func (r *WebhookResponse) SetIncorrectTransactionStatusError(message string) {
	r.Error = -7
	r.ErrorNote = message
}

func (r *WebhookResponse) SetUnsupportedActionError(message string) {
	r.Error = -3
	r.ErrorNote = message
}

func (r *WebhookResponse) SetTransactionCancelledError(message string) {
	r.Error = -9
	r.ErrorNote = message
}

func (r *WebhookResponse) SetUpdateTransactionError(message string) {
	r.Error = -7
	r.ErrorNote = message
}

func (r *WebhookResponse) SetTransactionAlreadyFinishedError(message string) {
	r.Error = -4
	r.ErrorNote = message
}

func (r *WebhookResponse) SetTransactionAlreadyCancelledError(message string) {
	r.Error = -9
	r.ErrorNote = message
}
