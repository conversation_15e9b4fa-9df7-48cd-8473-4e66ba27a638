package click

import (
	"fmt"
	"net/url"
)

type Client struct {
	MerchantID string
	ServiceID  string
}

func NewClient(merchantID, serviceID string) *Client {
	return &Client{
		MerchantID: merchantID,
		ServiceID:  serviceID,
	}
}

func (c *Client) GenerateSuperAppPayment(amount, transactionID int, auth bool, hideCross bool, returnPage string) string {
	// Create the base deep link
	deepLink := fmt.Sprintf("https://my.click.uz/app/webView?auth=%t&hide_cross=%t&url=", auth, hideCross)

	// Add the page to the deep link and encode it
	encodedDeepLinkWithPage := deepLink + url.QueryEscape(returnPage)

	// Encode the final deep link again
	encodedFinalDeepLink := url.QueryEscape(encodedDeepLinkWithPage)

	// Create the final URL
	finalURL := fmt.Sprintf(
		"https://my.click.uz/services/pay/?service_id=%s&merchant_id=%s&amount=%d&transaction_param=%d&return_url=%s",
		c.ServiceID,
		c.MerchantID,
		amount,
		transactionID,
		encodedFinalDeepLink,
	)

	return finalURL
}
