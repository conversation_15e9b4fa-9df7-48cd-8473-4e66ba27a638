package ubk_a2a

import (
	"time"
)

// methods
const (
	Login = "login"

	GetAccountDetails = "get.account.details"
	GetAccountHistory = "get.account.history"

	CreateTransaction     = "create.transaction"
	GetTransactionById    = "get.transaction"
	GetTransactionByExtId = "get.transaction.by.extid"
)

type Request struct {
	JsonRPC string `json:"jsonrpc"`
	Id      string `json:"id"`
	Method  string `json:"method"`
	Params  any    `json:"params"`
}

// -------- login

type LoginParam struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// ----------  transaction

type CreateA2ATransactionParam struct {
	Type       string  `json:"type"`
	ExternalId string  `json:"external_id,omitempty"`
	Sender     Account `json:"sender"`
	Recipient  Account `json:"recipient"`
	Purpose    struct {
		Code string `json:"code"`
		Name string `json:"name"`
	} `json:"purpose"`
	Amount int    `json:"amount"` // sum -> tiyin
	State  string `json:"state,omitempty"`
	ready  bool
}

func (r *CreateA2ATransactionParam) Prepare() {
	if !r.ready {
		r.Type = "106"
		r.Purpose.Code = "00668"
		r.Purpose.Name = "Бажарилган иш ва курсатилган хизматлар учун"
		r.Amount *= 100
		r.ready = true
	}
}

type GetTrByExtIdParam struct {
	ExternalId string `json:"external_id"`
}

type TrIdParam struct {
	TransactionId string `json:"transaction_id"`
}

// ------------   account methods

type Account struct {
	Account    string `json:"account"`
	CodeFilial string `json:"code_filial"`
	TaxID      string `json:"tax,omitempty"`
	Name       string `json:"name"`
}

type GetAccountDetailsParam struct {
	Account    string `json:"account"`
	CodeFilial string `json:"code_filial"`
}

func (r *GetAccountDetailsParam) Build(account, codeFilial string) {
	r.Account = account
	r.CodeFilial = codeFilial
}

type GetAccountHistoryParam struct {
	Account    string `json:"account"`
	CodeFilial string `json:"code_filial"`
	FromDate   string `json:"date_begin"`
	ToDate     string `json:"date_close"`
}

func (r *GetAccountHistoryParam) Build(fromDate, toDate time.Time) {
	r.FromDate = fromDate.Format("02.01.2006")
	r.ToDate = toDate.Format("02.01.2006")
}
