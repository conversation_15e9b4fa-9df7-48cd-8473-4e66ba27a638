package ubk_a2a

import (
	"billing_service/util"
	"fmt"
	"slices"

	"github.com/guregu/null/v6"
)

type ErrorResp struct {
	Code    int `json:"code"`
	Message any `json:"message"`
}

func (e *ErrorResp) GetMessage() string {
	if e.Message == nil {
		return ""
	}
	switch v := e.Message.(type) {
	case string:
		return v
	default:
		return fmt.Sprintf("%v", v)
	}
}

type GetAccountBody struct {
	AccountId   string     `json:"account_id"`
	Account     string     `json:"account"`
	AccountName string     `json:"name_acc"`
	TaxId       string     `json:"inn"`
	Currency    string     `json:"code_currency"`
	CodeFilial  string     `json:"code_filial"`
	NameFilial  string     `json:"name_filial"`
	ClientId    int        `json:"client_id"`
	Balance     null.Float `json:"saldo"` // in tiyins
}

type GetHistoryBody struct {
	Date          string     `json:"date"`
	Type          string     `json:"type"`
	Sender        Account    `json:"sender"`
	Recipient     Account    `json:"recipient"`
	Debit         null.Float `json:"debit"`
	Credit        null.Float `json:"credit"`
	TransactionId string     `json:"transaction_id"`
	ExternalId    string     `json:"external_id"`
	Purpose       string     `json:"purpose"`
}

type CoreResponse struct {
	JsonRPC string      `json:"jsonrpc"`
	Id      null.String `json:"id"`
	Error   *ErrorResp  `json:"error"`
	Status  null.Bool   `json:"status"`
}

type LoginResponse struct {
	CoreResponse
	Result struct {
		AccessToken string `json:"access_token"`
	} `json:"result"`
}

type CreateTransactionResponse struct {
	CoreResponse
	Result struct {
		Transaction struct {
			TransactionId null.String `json:"transaction_id"`
			ExternalId    null.String `json:"external_id"`
		} `json:"transaction"`
	} `json:"result"`
}

type GetAccountDetailsResponse struct {
	CoreResponse
	Result struct {
		Account *GetAccountBody `json:"account"`
	} `json:"result"`
}

type GetAccountHistoryResponse struct {
	CoreResponse
	Result struct {
		Transactions *[]GetHistoryBody `json:"transactions"`
	} `json:"result"`
}

type GetTransactionResponse struct {
	CoreResponse
	Result struct {
		Transaction struct {
			State         null.String `json:"state"`
			Sender        Account     `json:"sender"`
			Recipient     Account     `json:"recipient"`
			Amount        null.Float  `json:"amount"`
			ExternalId    null.String `json:"external_id"`
			TransactionId null.String `json:"transaction_id"`
		} `json:"transaction"`
	} `json:"result"`
}

func ExtractA2AStatus(bankState string) int {
	var state = util.ParseInt(bankState)
	switch {
	case slices.Contains(InProcessStates, state):
		return StatusStarted
	case slices.Contains(ErrorStates, state):
		return StatusRejected
	case state == StateCompleted:
		return StatusSuccess
	default:
		return 444
	}
}
