package ubk_a2a

import (
	"errors"
)

// a2a response codes and error codes, codes on jsonrpc request
const (
	SuccessResponseCode            = 0
	UnauthorizedResponseCode       = 401
	InternalErrorResponseCode      = 999
	InsufficientFundResponseCode   = -32603
	AccountIsIncorrectResponseCode = -32600
	ValidationErrorResponseCode    = 422
	MethodNotAllowedResponseCode   = -32105

	NotFoundResponseCode              = 404
	NotFoundResponseCodeAlt           = 2
	NoDataFoundResponseCode           = 8
	TransactionNotAllowedResponseCode = 998
)

// errors
var (
	NotFoundError              = errors.New("a2a transaction not found")
	InsufficientFundError      = errors.New("a2a insufficient fund")
	BankInternalError          = errors.New("a2a bank internal error")
	TransactionNotAllowedError = errors.New("a2a transaction not allowed")
	NetworkError               = errors.New("a2a network error")
	UnauthorizedError          = errors.New("a2a unauthorized")
	UnknownError               = errors.New("a2a unknown error")
	ApiTimeoutError            = errors.New("a2a api timeout")
	ValidationError            = errors.New("a2a validation error")
	NoDataFround               = errors.New("a2a no data round")
	MethodNotAllowed           = errors.New("a2a method not allowed")
	TransactionDeleted         = errors.New("a2a transaction deleted")
	//BankIsNotOperativeError    = errors.New("a2a bank is not operative")
)

// A2AStatuses Enum-like constants, the status codes on our db
const (
	StatusPending           = 30
	StatusSuccess           = 4
	StatusManualPaid        = 5
	StatusRejected          = 50
	StatusInsufficientFunds = 51
	StatusStarted           = 32
	StatusNotAllowed        = 33
	StatusNotFound          = 404
	StatusUnknownError      = 444
)

// TransferStatus Enum-like constants, transaction status given by bank
const (
	StatePlanned = 0
	StateEntered = 11
	StateDeleted = 13

	//StatePendingDeletion  = 15
	//StateForRevision      = 16
	//StateRejectedTransfer = 18

	StateErrorTransfer = 19

	//StatePostponed = 21

	StateApproved = 31

	//StateApprovedByCb = 32

	StateCompleted = 41
	StateSentToKc  = 50

	//StateAcceptedByKc            = 51
	//StateSentForVerification     = 53
	//StateAcceptedForVerification = 54
)

// 0 -> 11 -> 50 -> 51 -> 31 -> 41. 41 and 13 are final statuses

var ErrorStates = []int{StateDeleted}
var InProcessStates = []int{StatePlanned, StateEntered, StateSentToKc, StateApproved, StateSentToKc, StateErrorTransfer}
