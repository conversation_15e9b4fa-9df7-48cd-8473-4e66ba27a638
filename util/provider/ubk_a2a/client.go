package ubk_a2a

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/bytedance/sonic"
	"github.com/google/uuid"
)

var json = sonic.ConfigDefault

type Client struct {
	baseUrl        string
	login          string
	password       string
	Account        string
	Mfo            string
	TaxId          string
	token          string
	endpoint       string
	httpClientLong *http.Client
}

func NewClient(baseUrl, login, password, account, taxId string) *Client {
	return &Client{
		baseUrl:        baseUrl,
		login:          login,
		password:       password,
		Account:        account,
		Mfo:            "01186",
		TaxId:          taxId,
		endpoint:       "/api/v1/gate",
		httpClientLong: &http.Client{Timeout: 60 * time.Second},
	}
}

func ParseAndHandleResponse[T any](resp *http.Response) (T, error) {
	var response T
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return response, fmt.Errorf("a2a api returned error status: %d, body: %s", resp.StatusCode, string(body))
	}

	err := json.NewDecoder(resp.Body).Decode(&response)
	if err != nil {
		return response, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	return response, nil
}
func (c *Client) Authenticate(ctx context.Context) (err error) {
	response, err := c.Login(ctx)
	if err != nil {
		return fmt.Errorf("failed to authenticate: %w", err)
	}

	if (response.Result.AccessToken) != "" {
		c.token = response.Result.AccessToken
	} else {
		return fmt.Errorf("login response missing valid token")
	}

	return nil
}

func (c *Client) Login(ctx context.Context) (LoginResponse, error) {
	params := LoginParam{
		Username: c.login,
		Password: c.password,
	}

	payload := Request{
		JsonRPC: "2.0",
		Id:      uuid.New().String(),
		Method:  Login,
		Params:  params,
	}

	reqBody, err := json.Marshal(payload)
	if err != nil {
		return LoginResponse{}, fmt.Errorf("failed to marshal login payload: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", c.baseUrl+c.endpoint, bytes.NewReader(reqBody))
	if err != nil {
		return LoginResponse{}, fmt.Errorf("failed to create login request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClientLong.Do(req)
	if err != nil {
		return LoginResponse{}, fmt.Errorf("failed to send login request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return LoginResponse{}, fmt.Errorf("login request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var loginResponse LoginResponse
	if err := json.NewDecoder(resp.Body).Decode(&loginResponse); err != nil {
		return LoginResponse{}, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	if loginResponse.Error != nil {
		return loginResponse, c.ExtractError(loginResponse.Error)
	}

	return loginResponse, nil
}

func (c *Client) sendRequest(ctx context.Context, method string, request any) (resp *http.Response, err error) {
	payload := Request{
		JsonRPC: "2.0",
		Id:      uuid.New().String(),
		Method:  method,
		Params:  request,
	}

	reqBody, err := json.Marshal(payload)

	if err != nil {
		return
	}
	req, err := http.NewRequestWithContext(ctx, "POST", c.baseUrl+c.endpoint, bytes.NewReader(reqBody))
	if err != nil {
		return
	}

	if c.token == "" {
		if err := c.Authenticate(ctx); err != nil {
			return nil, fmt.Errorf("authentication failed: %w", err)
		}
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.token))
	req.Header.Set("Content-Type", "application/json")

	resp, err = c.httpClientLong.Do(req)
	if err != nil {
		fmt.Printf("error on sending a2a request: %s", err)
	}

	return resp, err
}

func (c *Client) GetAccountDetails(ctx context.Context, accNum string) (GetAccountDetailsResponse, error) {
	var resp GetAccountDetailsResponse
	var err error
	params := GetAccountDetailsParam{
		Account:    c.Account,
		CodeFilial: c.Mfo,
	}
	if accNum != "" { // unipos details
		params.Account = accNum
	}
	response, err := c.sendRequest(ctx, GetAccountDetails, params)
	if err != nil {
		return resp, fmt.Errorf("failed to send request: %w", err)
	}

	resp, err = ParseAndHandleResponse[GetAccountDetailsResponse](response)
	if err != nil {
		return resp, err
	}

	if resp.Error != nil {
		return resp, c.ExtractError(resp.Error)
	}

	return resp, nil
}

func (c *Client) GetAccHistory(ctx context.Context, dateBegin, dateClose time.Time) (resp GetAccountHistoryResponse, err error) {
	params := GetAccountHistoryParam{
		Account:    c.Account,
		CodeFilial: c.Mfo,
	}
	params.Build(dateBegin, dateClose)

	response, err := c.sendRequest(ctx, GetAccountHistory, params)
	if err != nil {
		return resp, fmt.Errorf("error on a2a get acc history: %w", err)
	}

	resp, err = ParseAndHandleResponse[GetAccountHistoryResponse](response)
	if err != nil {
		return resp, err
	}

	if resp.Error != nil {
		return resp, c.ExtractError(resp.Error)
	}

	return resp, nil
}

func (c *Client) CreateTransaction(ctx context.Context, sender, recipient Account, amount int, externalId string) (resp CreateTransactionResponse, err error) {
	createParams := CreateA2ATransactionParam{
		Sender:     sender,
		Recipient:  recipient,
		Amount:     amount,
		ExternalId: externalId,
	}
	createParams.Prepare()

	response, err := c.sendRequest(ctx, CreateTransaction, createParams)
	if err != nil {
		return resp, fmt.Errorf("error on a2a create transaction request: %w", err)
	}

	resp, err = ParseAndHandleResponse[CreateTransactionResponse](response)
	if err != nil {
		return resp, err
	}

	if resp.Error != nil {
		return resp, c.ExtractError(resp.Error)
	}

	return resp, nil
}

func (c *Client) GetTransaction(ctx context.Context, trId string) (resp GetTransactionResponse, err error) {
	params := TrIdParam{
		TransactionId: trId,
	}

	response, err := c.sendRequest(ctx, GetTransactionById, params)
	if err != nil {
		return resp, fmt.Errorf("error on a2a get transaction by id: %w", err)
	}

	resp, err = ParseAndHandleResponse[GetTransactionResponse](response)
	if err != nil {
		return resp, err
	}

	if resp.Error != nil {
		return resp, c.ExtractError(resp.Error)
	}

	return resp, nil
}

func (c *Client) GetTransactionByExtId(ctx context.Context, extId string) (resp GetTransactionResponse, err error) {
	params := GetTrByExtIdParam{
		ExternalId: extId,
	}

	response, err := c.sendRequest(ctx, GetTransactionByExtId, params)
	if err != nil {
		return resp, fmt.Errorf("error on a2a get transaction by ext id: %w", err)
	}

	resp, err = ParseAndHandleResponse[GetTransactionResponse](response)
	if err != nil {
		return resp, err
	}

	if resp.Error != nil {
		return resp, c.ExtractError(resp.Error)
	}
	return resp, nil
}

func (c *Client) CheckTransactionStatus(ctx context.Context, externalId string) (int, error) {
	trx, err := c.GetTransactionByExtId(ctx, externalId)

	if err != nil {
		return -1, err
	}
	state := trx.Result.Transaction.State
	status := ExtractA2AStatus(state.String)
	return status, nil
}

func (c *Client) GetBalance(ctx context.Context, accNum string) (int, error) {
	details, err := c.GetAccountDetails(ctx, accNum)
	if err != nil {
		return 0, fmt.Errorf("error getting details: %v", err)
	}

	return int(details.Result.Account.Balance.Float64 / 100), nil
}

func (c *Client) ExtractError(errInfo *ErrorResp) error {
	if errInfo == nil {
		return nil
	}
	fmt.Printf("a2a api error: %s\n", errInfo.GetMessage())

	switch errInfo.Code {
	case SuccessResponseCode:
		return nil
	case UnauthorizedResponseCode:
		return fmt.Errorf("%w: %s", UnauthorizedError, errInfo.GetMessage())
	case InternalErrorResponseCode:
		return fmt.Errorf("%w: %s", BankInternalError, errInfo.GetMessage())
	case InsufficientFundResponseCode:
		return fmt.Errorf("%w: %s", InsufficientFundError, errInfo.GetMessage())
	case AccountIsIncorrectResponseCode, ValidationErrorResponseCode:
		return fmt.Errorf("%w: %s", ValidationError, errInfo.GetMessage())
	case MethodNotAllowedResponseCode:
		return fmt.Errorf("%w: %s", MethodNotAllowed, errInfo.GetMessage())
	case NotFoundResponseCode, NotFoundResponseCodeAlt:
		return fmt.Errorf("%w: %s", NotFoundError, errInfo.GetMessage())
	case TransactionNotAllowedResponseCode:
		return fmt.Errorf("%w: %s", TransactionNotAllowedError, errInfo.GetMessage())
	case NoDataFoundResponseCode:
		return fmt.Errorf("%w: %s", NoDataFround, errInfo.GetMessage())
	default:
		return fmt.Errorf("%w: %s", UnknownError, errInfo.GetMessage())
	}
}
