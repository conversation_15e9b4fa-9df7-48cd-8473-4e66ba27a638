package paynet

import (
	"fmt"
	"time"
)

// request

type WehookRequest struct {
	Method string       `json:"method"`
	ID     int          `json:"id"`
	Params WehookParams `json:"params"`
}

type WehookParams struct {
	ServiceID     int          `json:"serviceId"`
	TransactionID int          `json:"transactionId"`
	Amount        int          `json:"amount"`
	Timestamp     string       `json:"timestamp"`
	DateFrom      string       `json:"dateFrom"`
	DateTo        string       `json:"dateTo"`
	Fields        WehookFields `json:"fields"`
}

type WehookFields struct {
	DriverID string `json:"driver_id"`
}

// response

type WebhookResponse struct {
	ID      int            `json:"id,omitempty"`
	JsonRPC string         `json:"jsonrpc,omitempty"`
	Result  *WebhookResult `json:"result,omitempty"`
	Error   *WebhookError  `json:"error,omitempty"`
}

type WebhookResult struct {
	Status           string         `json:"status,omitempty"`
	State            int            `json:"state,omitempty"`
	TransactionState int            `json:"transactionState,omitempty"`
	ProviderTrnId    int            `json:"providerTrnId,omitempty"`
	Timestamp        string         `json:"timestamp,omitempty"`
	Fields           map[string]any `json:"fields,omitempty"`
	Statements       []Statements   `json:"statements,omitempty"`
}

type Statements struct {
	Amount        int       `json:"amount"`
	ProviderTrnID int       `json:"providerTrnId"`
	Timestamp     time.Time `json:"timestamp"`
	TransactionID int       `json:"transactionId"`
}

type WebhookError struct {
	Code    int    `json:"code"`
	Message string `json:"message,omitempty"`
}

func (r *WebhookResponse) SetGetInformationResponse(balance int, name string, id int) {
	r.Result = &WebhookResult{
		Status:    "0",
		Timestamp: currentTimestamp(),
		Fields: map[string]any{
			"balance": balance,
			"name":    name,
		},
	}
	r.ID = id
	r.JsonRPC = "2.0"
}

func (r *WebhookResponse) SetPerformTransactionResponse(providerTrnId int, driverId string, id int) {
	r.Result = &WebhookResult{
		ProviderTrnId: providerTrnId,
		Timestamp:     currentTimestamp(),
		Fields: map[string]any{
			"driver_id": driverId,
		},
	}
	r.ID = id
	r.JsonRPC = "2.0"
}

func (r *WebhookResponse) SetCheckTransactionResponse(providerTrnId int, transactionState, id int) {
	r.Result = &WebhookResult{
		TransactionState: transactionState,
		ProviderTrnId:    providerTrnId,
		Timestamp:        currentTimestamp(),
	}
	r.ID = id
	r.JsonRPC = "2.0"
}

func (r *WebhookResponse) SetCancelTransactionResponse(providerTrnId int, transactionState, id int) {
	r.Result = &WebhookResult{
		TransactionState: transactionState,
		ProviderTrnId:    providerTrnId,
		Timestamp:        currentTimestamp(),
	}
	r.ID = id
	r.JsonRPC = "2.0"
}

func (r *WebhookResponse) SetGetStatementResponse(statements []Statements, id int) {
	if statements == nil {
		statements = make([]Statements, 0)
	}
	r.Result = &WebhookResult{
		Statements: statements,
	}
	r.ID = id
	r.JsonRPC = "2.0"
}

// Error response methods
func (r *WebhookResponse) SetJSONParsingError(error string) {
	r.Error = &WebhookError{
		Code:    -32700,
		Message: fmt.Sprintf("JSON parsing error: %v", error),
	}
}

func (r *WebhookResponse) SetMethodNotFoundError() {
	r.Error = &WebhookError{
		Code:    -32601,
		Message: "Requested method not found.",
	}
}

func (r *WebhookResponse) SetInternalError(error string) {
	r.Error = &WebhookError{
		Code:    -32603,
		Message: fmt.Sprintf("Internal system error: %v", error),
	}
}

func (r *WebhookResponse) SetServiceUnavailableError() {
	r.Error = &WebhookError{
		Code:    100,
		Message: "Service temporarily unavailable.",
	}
}

func (r *WebhookResponse) SetTransactionExistsError() {
	r.Error = &WebhookError{
		Code:    201,
		Message: "Transaction already exists.",
	}
}

func (r *WebhookResponse) SetTransactionAlreadyCancelledError() {
	r.Error = &WebhookError{
		Code:    202,
		Message: "Transaction already cancelled.",
	}
}

func (r *WebhookResponse) SetTransactionNotFoundError() {
	r.Error = &WebhookError{
		Code:    203,
		Message: "Transaction not found.",
	}
}

func (r *WebhookResponse) SetDriverNotFoundError() {
	r.Error = &WebhookError{
		Code:    302,
		Message: "Driver not found.",
	}
}

func (r *WebhookResponse) SetInvalidLoginOrPassword() {
	r.Error = &WebhookError{
		Code:    412,
		Message: "Invalid login or password.",
	}
}

func currentTimestamp() string {
	return time.Now().Format("2006-01-02 15:04:05")
}
