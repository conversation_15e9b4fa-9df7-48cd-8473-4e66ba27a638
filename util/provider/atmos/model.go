package atmos

import "encoding/json"

type AuthResponse struct {
	AccessToken string `json:"access_token"`
	Scope       string `json:"scope"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
}

type CardBindRequest struct {
	RequestID  string `json:"request_id"`
	StoreID    int    `json:"store_id"`
	Account    int    `json:"account"`
	SuccessURL string `json:"success_url"`
}

type CardBindResponse struct {
	StoreID   int    `json:"store_id"`
	PaymentID int    `json:"payment_id"`
	Token     string `json:"token"`
	URL       string `json:"url"`
	Status    struct {
		Code        string `json:"code"`
		Description string `json:"description"`
	} `json:"status"`
}

type CardDetailsResponse struct {
	Payload struct {
		Card struct {
			ID               int    `json:"id"`
			Status           bool   `json:"status"`
			Approved         bool   `json:"approved"`
			MaskedPan        string `json:"masked_pan"`
			MaskedCardHolder string `json:"masked_card_holder"`
			CardType         string `json:"card_type"`
			DateCreated      string `json:"date_created"`
			DateUpdated      string `json:"date_updated"`
			VerifiedState    string `json:"verified_state"`
			Status3ds        string `json:"status_3ds"`
			CardRegion       string `json:"card_region"`
			ApprovalReturned bool   `json:"approval_returned"`
			StoreID          int    `json:"store_id"`
		} `json:"card"`
		CardID int `json:"card_id"`
	} `json:"payload"`
	Status struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		TraceID string `json:"trace_id"`
	} `json:"status"`
}

type CardDetailsWithHashResponse struct {
	Payload struct {
		ID               int    `json:"id"`
		Status           bool   `json:"status"`
		Approved         bool   `json:"approved"`
		MaskedPan        string `json:"masked_pan"`
		MaskedCardHolder string `json:"masked_card_holder"`
		CardType         string `json:"card_type"`
		DateCreated      string `json:"date_created"`
		DateUpdated      string `json:"date_updated"`
		VerifiedState    string `json:"verified_state"`
		CardRegion       string `json:"card_region"`
		ApprovalReturned bool   `json:"approval_returned"`
		StoreID          int    `json:"store_id"`
		UniqueCardToken  string `json:"unique_card_token"`
	} `json:"payload"`
	Status struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		TraceID string `json:"trace_id"`
	} `json:"status"`
}

type Status struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	TraceID string `json:"trace_id,omitempty"`
}

func (s *Status) IsSuccess() bool {
	return s.Code == 0
}

func (s *Status) ToError() error {
	if s.IsSuccess() {
		return nil
	}

	// Create a TransactionError instead of AtmosError
	return NewTransactionError(TransactionStatusFailed, s.Message, true, false)
}

type CardDetails struct {
	ID               int    `json:"id"`
	Status           bool   `json:"status"`
	Approved         bool   `json:"approved"`
	MaskedPan        string `json:"masked_pan"`
	MaskedCardHolder string `json:"masked_card_holder"`
	CardType         string `json:"card_type"`
	DateCreated      string `json:"date_created"`
	DateUpdated      string `json:"date_updated"`
	VerifiedState    string `json:"verified_state"`
	Status3DS        string `json:"status_3ds"`
	CardRegion       string `json:"card_region"`
	ApprovalReturned bool   `json:"approval_returned"`
	StoreID          int    `json:"store_id"`
}

type UklonHoldRequest struct {
	ExtID        string `json:"ext_id"`
	Amount       int    `json:"amount"`
	StoreID      string `json:"store_id"`
	Account      string `json:"account"`
	InvoiceID    string `json:"invoice_id"`
	CardID       int    `json:"card_id"`
	ClientIPAddr string `json:"client_ip_addr"`
}

type UklonApplyRequest struct {
	TransactionID string `json:"transaction_id"`
}

type UklonCancelRequest struct {
	StoreID       string `json:"store_id"`
	TransactionID string `json:"transaction_id"`
}

type TransactionPayload struct {
	ID              int         `json:"id"`
	Status          int         `json:"status"`
	RRN             string      `json:"rrn"`
	Type            string      `json:"type"`
	Amount          int         `json:"amount"`
	Card            CardDetails `json:"card"`
	CardID          int         `json:"card_id"`
	ExternalID      string      `json:"external_id"`
	MPSExtID        string      `json:"mps_ext_id"`
	StatusPS        string      `json:"status_ps"`
	Status3DS       string      `json:"status_3ds"`
	ResultCode      string      `json:"result_code"`
	MaskedPan       string      `json:"masked_pan"`
	ClientIPAddr    string      `json:"client_ip_addr"`
	DateCreated     string      `json:"date_created"`
	DateUpdated     string      `json:"date_updated"`
	RedirectURI     string      `json:"redirect_uri,omitempty"`
	OFDRedirectURI  string      `json:"ofd_redirect_uri"`
	StoreID         int         `json:"store_id"`
	UpperCommission int         `json:"upper_commission"`
	LowerCommission int         `json:"lower_commission"`
}

func (p *TransactionPayload) GetTransactionStatus() TransactionStatus {
	return TransactionStatus(p.Status)
}

func (p *TransactionPayload) IsSuccess() bool {
	return p.GetTransactionStatus().IsSuccess()
}

func (p *TransactionPayload) IsPending() bool {
	return p.GetTransactionStatus().IsPending()
}

func (p *TransactionPayload) IsFailed() bool {
	return p.GetTransactionStatus().IsFailed()
}

func (p *TransactionPayload) IsReported() bool {
	return p.GetTransactionStatus().IsReported()
}

func (p *TransactionPayload) ToError() error {
	status := p.GetTransactionStatus()
	if status.IsSuccess() {
		return nil
	}
	final := false
	critical := false

	switch status {
	case TransactionStatusFailed:
		final = true
	case TransactionStatusCancelled:
		final = true

	case TransactionStatusFailedCredit,
		TransactionStatusFailedBlacklist, TransactionStatusFailedABS,
		TransactionStatusFailedQueuedCredit, TransactionStatusFailedQueuedABS:
		final = false
		critical = false
	}

	return NewTransactionError(status, p.ResultCode, final, critical)
}

type TransactionResponse struct {
	Payload TransactionPayload `json:"payload"`
	Status  Status             `json:"status"`
}

func (r *TransactionResponse) ToError() error {
	if err := r.Status.ToError(); err != nil {
		return err
	}

	return r.Payload.ToError()
}

type UklonCancelResponse struct {
	Status Status `json:"status"`
}

func (r *UklonCancelResponse) ToError() error {
	return r.Status.ToError()
}

type WebhookRequest struct {
	StoreID       string      `json:"store_id"`
	TransactionID string      `json:"transaction_id"`
	Sign          string      `json:"sign"`
	Invoice       json.Number `json:"invoice"`
	CardID        string      `json:"card_id"`
}
