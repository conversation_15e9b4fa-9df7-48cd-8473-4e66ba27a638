package atmos

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/google/uuid"
)

func (c *Client) CardAdd(ctx context.Context, userId int) (resp CardBindResponse, err error) {
	requestID := uuid.NewString()

	req := CardBindRequest{
		RequestID:  requestID,
		StoreID:    c.storeID,
		Account:    userId,
		SuccessURL: "https://mytaxi.uz",
	}

	respBody, err := c.sendRequest(ctx, cardBindEndpoint, http.MethodPost, req)
	if err != nil {
		return resp, fmt.Errorf("card add request: %v", err)
	}

	err = json.Unmarshal(respBody, &resp)
	if err != nil {
		return resp, fmt.Errorf("parse card add response: %v", err)
	}

	if resp.Status.Code != "0" {
		return resp, fmt.Errorf("card add failed: %s", resp.Status.Description)
	}

	return
}

func (c *Client) GetCardDetails(ctx context.Context, cardID string) (resp CardDetailsResponse, err error) {
	endpoint := fmt.Sprintf("/mps/card/%s", cardID)

	respBody, err := c.sendRequest(ctx, endpoint, http.MethodGet, nil)
	if err != nil {
		return resp, fmt.Errorf("get card details request: %v", err)
	}

	err = json.Unmarshal(respBody, &resp)
	if err != nil {
		return resp, fmt.Errorf("parse card details response: %v", err)
	}

	if resp.Status.Code != 0 {
		return resp, fmt.Errorf("get card details failed: %s", resp.Status.Message)
	}

	return
}

// GetCardDetailsWithHash retrieves card details including the unique_card_token (hash)
func (c *Client) GetCardDetailsWithHash(ctx context.Context, cardID string) (resp CardDetailsWithHashResponse, err error) {
	endpoint := fmt.Sprintf("/mps/card/info/%s", cardID)

	respBody, err := c.sendRequest(ctx, endpoint, http.MethodGet, nil)
	if err != nil {
		return resp, fmt.Errorf("get card details with hash request: %v", err)
	}

	err = json.Unmarshal(respBody, &resp)
	if err != nil {
		return resp, fmt.Errorf("parse card details with hash response: %v", err)
	}

	if resp.Status.Code != 0 {
		return resp, fmt.Errorf("get card details with hash failed: %s", resp.Status.Message)
	}

	return
}
