package atmos

// SoumToTiyin converts an amount in Uzbekistan Soum to tiyin (1/100 of a Soum)
func SoumToTiyin(amount int) int {
	return amount * 100
}

// TiyinToSoum converts an amount in tiyin to Uzbekistan Soum
func TiyinToSoum(amount int) int {
	return amount / 100
}

// FormatMaskedPAN standardizes the masked PAN format to ensure it's not longer than 16 characters
// It extracts the first 6 and last 4 digits and adds exactly 6 asterisks in between
func FormatMaskedPAN(maskedPan string) string {
	// If the masked PAN is longer than 16 characters, we need to adjust the number of asterisks
	if len(maskedPan) > 16 {
		// Extract the first 6 and last 4 digits
		firstSix := maskedPan[:6]
		lastFour := maskedPan[len(maskedPan)-4:]

		// Create a properly formatted masked PAN with exactly 6 asterisks
		return firstSix + "******" + lastFour
	}

	return maskedPan
}
