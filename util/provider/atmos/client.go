package atmos

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"
)

const (
	defaultTimeout   = 5 * time.Second
	authEndpoint     = "/token?grant_type=client_credentials"
	cardBindEndpoint = "/checkout/card-bind/create"

	// Transaction endpoints
	uklonHoldEndpoint      = "/mps-extend/uklon/hold"
	uklonApplyEndpoint     = "/mps-extend/uklon/apply"
	uklonCancelEndpoint    = "/mps-extend/uklon/cancel"
	transactionGetEndpoint = "/mps/transaction/get/"
)

type Client struct {
	baseURL        string
	clientID       string
	clientSecret   string
	storeID        int
	httpClient     *http.Client
	httpClientLong *http.Client

	token       string
	tokenExpiry time.Time
	tokenMutex  sync.Mutex
}

func NewClient(baseURL, clientID, clientSecret string, storeID int) *Client {
	return &Client{
		baseURL:        baseURL,
		clientID:       clientID,
		clientSecret:   clientSecret,
		storeID:        storeID,
		httpClient:     &http.Client{Timeout: defaultTimeout},
		httpClientLong: &http.Client{Timeout: 60 * time.Second},
	}
}

func (c *Client) getAuthorizationHeader() string {
	auth := c.clientID + ":" + c.clientSecret
	return "Basic " + base64.StdEncoding.EncodeToString([]byte(auth))
}

func (c *Client) getToken(ctx context.Context) (string, error) {
	c.tokenMutex.Lock()
	defer c.tokenMutex.Unlock()

	if c.token != "" && time.Now().Before(c.tokenExpiry) {
		return c.token, nil
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, c.baseURL+authEndpoint, nil)
	if err != nil {
		return "", err
	}

	req.Header.Set("Authorization", c.getAuthorizationHeader())
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("auth failed: %s, body: %s", resp.Status, string(body))
	}

	var authResp AuthResponse
	if err := json.NewDecoder(resp.Body).Decode(&authResp); err != nil {
		return "", err
	}

	c.token = authResp.AccessToken
	c.tokenExpiry = time.Now().Add(time.Duration(authResp.ExpiresIn-60) * time.Second)

	return c.token, nil
}

func (c *Client) sendRequest(ctx context.Context, endpoint string, method string, body any) ([]byte, error) {
	respBody, err := c.doRequest(ctx, endpoint, method, body)
	if err != nil {
		if strings.Contains(err.Error(), "401") || strings.Contains(err.Error(), "Invalid Credentials") {
			fmt.Printf("Token error detected: %v. Refreshing token and retrying...", err)
			c.tokenMutex.Lock()
			c.token = ""
			c.tokenMutex.Unlock()

			return c.doRequest(ctx, endpoint, method, body)
		}
	}

	return respBody, err
}

func (c *Client) doRequest(ctx context.Context, endpoint string, method string, body any) ([]byte, error) {
	token, err := c.getToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("get token: %v", err)
	}

	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}

	req, err := http.NewRequestWithContext(ctx, method, c.baseURL+endpoint, bodyReader)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClientLong.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed: %s, body: %s", resp.Status, string(respBody))
	}

	return respBody, nil
}

func (c *Client) UklonHold(ctx context.Context, req UklonHoldRequest) (TransactionResponse, error) {
	var resp TransactionResponse

	respBody, err := c.sendRequest(ctx, uklonHoldEndpoint, http.MethodPost, req)
	if err != nil {
		return resp, fmt.Errorf("uklon hold request: %v", err)
	}

	err = json.Unmarshal(respBody, &resp)
	if err != nil {
		return resp, fmt.Errorf("parse uklon hold response: %v", err)
	}
	return resp, nil
}

func (c *Client) UklonApply(ctx context.Context, transactionID string) (TransactionResponse, error) {
	var resp TransactionResponse

	req := UklonApplyRequest{
		TransactionID: transactionID,
	}

	respBody, err := c.sendRequest(ctx, uklonApplyEndpoint, http.MethodPost, req)
	if err != nil {
		return resp, fmt.Errorf("uklon apply request: %v", err)
	}

	err = json.Unmarshal(respBody, &resp)
	if err != nil {
		return resp, fmt.Errorf("parse uklon apply response: %v", err)
	}

	return resp, nil
}

func (c *Client) UklonCancel(ctx context.Context, storeID string, transactionID string) (UklonCancelResponse, error) {
	var resp UklonCancelResponse

	req := UklonCancelRequest{
		StoreID:       storeID,
		TransactionID: transactionID,
	}

	respBody, err := c.sendRequest(ctx, uklonCancelEndpoint, http.MethodPost, req)
	if err != nil {
		return resp, fmt.Errorf("uklon cancel request: %v", err)
	}

	err = json.Unmarshal(respBody, &resp)
	if err != nil {
		return resp, fmt.Errorf("parse uklon cancel response: %v", err)
	}
	return resp, nil
}

func (c *Client) GetTransaction(ctx context.Context, transactionID string) (TransactionResponse, error) {
	var resp TransactionResponse

	respBody, err := c.sendRequest(ctx, transactionGetEndpoint+transactionID, http.MethodGet, nil)
	if err != nil {
		return resp, fmt.Errorf("get transaction request: %v", err)
	}

	err = json.Unmarshal(respBody, &resp)
	if err != nil {
		return resp, fmt.Errorf("parse transaction response: %v", err)
	}

	if err := resp.ToError(); err != nil {
		return resp, fmt.Errorf("get transaction failed: %v", err)
	}

	return resp, nil
}
