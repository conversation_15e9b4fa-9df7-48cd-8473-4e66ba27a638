package atmos

import (
	"fmt"
)

type TransactionStatus int

const (
	TransactionStatusNew                TransactionStatus = 1
	TransactionStatusAccepted           TransactionStatus = 2
	TransactionStatusConfirmed          TransactionStatus = 3
	TransactionStatusFinished           TransactionStatus = 4
	TransactionStatusFailed             TransactionStatus = 5
	TransactionStatusPending            TransactionStatus = 10
	TransactionStatusPendingABS         TransactionStatus = 11
	TransactionStatusFailedBlacklist    TransactionStatus = 12
	TransactionStatusFailedCredit       TransactionStatus = 13
	TransactionStatusFailedABS          TransactionStatus = 14
	TransactionStatusFailedQueuedCredit TransactionStatus = 15
	TransactionStatusFailedQueuedABS    TransactionStatus = 16
	TransactionStatusReported           TransactionStatus = -100
	TransactionStatusCancelled          TransactionStatus = -101
)

func (s TransactionStatus) String() string {
	switch s {
	case TransactionStatusNew:
		return "NEW"
	case TransactionStatusAccepted:
		return "ACCEPTED"
	case TransactionStatusConfirmed:
		return "CONFIRMED"
	case TransactionStatusFinished:
		return "FINISHED"
	case TransactionStatusFailed:
		return "FAILED"
	case TransactionStatusPending:
		return "PENDING"
	case TransactionStatusPendingABS:
		return "PENDING_ABS"
	case TransactionStatusFailedBlacklist:
		return "FAILED_BLACKLIST"
	case TransactionStatusFailedCredit:
		return "FAILED_CREDIT"
	case TransactionStatusFailedABS:
		return "FAILED_ABS"
	case TransactionStatusFailedQueuedCredit:
		return "FAILED_QUEUED_CREDIT"
	case TransactionStatusFailedQueuedABS:
		return "FAILED_QUEUED_ABS"
	case TransactionStatusReported:
		return "REPORTED"
	case TransactionStatusCancelled:
		return "CANCELLED"
	default:
		return fmt.Sprintf("UNKNOWN_STATUS(%d)", s)
	}
}

func (s TransactionStatus) IsSuccess() bool {
	return s == TransactionStatusFinished || s == TransactionStatusConfirmed
}

func (s TransactionStatus) IsPending() bool {
	return s == TransactionStatusNew || s == TransactionStatusAccepted ||
		s == TransactionStatusPending || s == TransactionStatusPendingABS ||
		s == TransactionStatusFailedBlacklist || s == TransactionStatusFailedCredit ||
		s == TransactionStatusFailedABS || s == TransactionStatusFailedQueuedCredit ||
		s == TransactionStatusFailedQueuedABS
}

func (s TransactionStatus) IsFailed() bool {
	return s == TransactionStatusFailed ||
		s == TransactionStatusCancelled
}

func (s TransactionStatus) IsReported() bool {
	return s == TransactionStatusReported
}

// No ErrorCode or AtmosError related code needed anymore

type TransactionError struct {
	Status   TransactionStatus
	Message  string
	Final    bool
	Critical bool
}

func (e *TransactionError) Error() string {
	finalStr := ""
	if e.Final {
		finalStr = " (final)"
	}

	criticalStr := ""
	if e.Critical {
		criticalStr = " (critical)"
	}

	return fmt.Sprintf("Transaction error: %s (%d)%s%s - %s",
		e.Status.String(), e.Status, finalStr, criticalStr, e.Message)
}

func NewTransactionError(status TransactionStatus, message string, final, critical bool) *TransactionError {
	return &TransactionError{
		Status:   status,
		Message:  message,
		Final:    final,
		Critical: critical,
	}
}

func IsTransactionError(err error) bool {
	_, ok := err.(*TransactionError)
	return ok
}

func GetTransactionError(err error) *TransactionError {
	if err == nil {
		return nil
	}

	if txErr, ok := err.(*TransactionError); ok {
		return txErr
	}

	return nil
}

func IsFinalError(err error) bool {
	if txErr := GetTransactionError(err); txErr != nil {
		return txErr.Final
	}

	return true
}

func IsCriticalError(err error) bool {
	if txErr := GetTransactionError(err); txErr != nil {
		return txErr.Critical
	}

	return false
}
