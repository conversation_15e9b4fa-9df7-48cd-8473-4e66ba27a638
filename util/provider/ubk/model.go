package ubk

const (
	PaymentStatusCreated     = 0
	PaymentStatusSuccess     = 4
	PaymentStatusInProgress1 = 29
	PaymentStatusInProgress2 = 30
)

type Request struct {
	JsonRPC string `json:"jsonrpc,omitempty"`
	Id      string `json:"id,omitempty"`
	Method  string `json:"method"`
	Params  Params `json:"params"`
}

type Params struct {
	Amount        int    `json:"amount,omitempty"`
	TransactionId string `json:"ext_id,omitempty"`
	CardNumber    string `json:"number,omitempty"`
	Account       string `json:"account,omitempty"`
	Username      string `json:"username,omitempty"`
	Password      string `json:"password,omitempty"`
	Input         string `json:"input,omitempty"`
	// Token         string `json:"token,omitempty"`
	// Comment       string `json:"description,omitempty"`
}

type Response struct {
	// JsonRPC string `json:"jsonrpc"`
	// ID     string `json:"id"`
	// Origin string `json:"origin"`
	// Host   Host   `json:"host"`
	Result Result `json:"result"`
	Status bool   `json:"status"`
	Error  *Error `json:"error"`
}

type Result struct {
	ExtId        string `json:"ext_id"`
	State        int    `json:"state"`
	ResponseBody struct {
		Balance string `json:"saldo"`
	} `json:"responseBody,omitzero"`
	AccessToken string `json:"access_token,omitempty"`
	// Number      string    `json:"number"`
	// Description string    `json:"description"`
	// Amount      int       `json:"amount"`
	// Currency    string    `json:"currency"`
	// Commission  float64   `json:"commission"`
	// Account     []Account `json:"account"`
	// Payment     Payment   `json:"payment"`
	// Merchant    Merchant  `json:"merchant"`
}

type Error struct {
	Message any `json:"message"`
	Code    int `json:"code"`
}
