package ubk

import (
	"context"
	"net/http"

	"github.com/google/uuid"
)

func (c *Client) CreateCheck(ctx context.Context, params Params) (resp Response, err error) {
	params.Amount = SoumToTiyin(params.Amount)
	req := Request{
		JsonRPC: "2.0",
		Id:      uuid.NewString(),
		Method:  "transfer.credit.create",
		Params:  params,
	}
	resp, err = c.sendRequest(ctx, c.baseUrl, req, http.MethodPost)
	return
}

func (c *Client) PayCheck(ctx context.Context, transactionId string) (resp Response, err error) {
	req := Request{
		JsonRPC: "2.0",
		Id:      uuid.NewString(),
		Method:  "transfer.credit.confirm",
		Params: Params{
			TransactionId: transactionId,
		},
	}
	resp, err = c.sendRequest(ctx, c.baseUrl, req, http.MethodPost)
	return
}

func (c *Client) CheckStatus(ctx context.Context, transactionId string) (resp Response, err error) {
	req := Request{
		JsonRPC: "2.0",
		Id:      uuid.NewString(),
		Method:  "transfer.credit.state",
		Params: Params{
			TransactionId: transactionId,
		},
	}
	resp, err = c.sendRequest(ctx, c.baseUrl, req, http.MethodPost)
	return
}

func (c *Client) CancelPay(ctx context.Context, transactionId string) (resp Response, err error) {
	req := Request{
		JsonRPC: "2.0",
		Id:      uuid.NewString(),
		Method:  "transfer.credit.cancel",
		Params: Params{
			TransactionId: transactionId,
		},
	}
	resp, err = c.sendRequest(ctx, c.baseUrl, req, http.MethodPost)
	return
}
