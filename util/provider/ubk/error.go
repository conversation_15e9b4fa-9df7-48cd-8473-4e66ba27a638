package ubk

import (
	"errors"
	"fmt"
)

const (
	ServiceUnavailableCode = 500
	UnauthorizedCode       = -32102
	InvalidRequestCode     = -32600
	ReceiptNotFoundCode    = -32402
	InvalidToken           = -32101
	ExpiredToken           = -32103
	ExpiredTokenV2         = -36042
)

var (
	ErrUnknown               = errors.New("unknown error")
	Errunauthorized          = errors.New("unauthorized")
	ErrUbkServiceUnavailable = errors.New("ubk service unvailable")
	ErrTimeoutExceeded       = errors.New("timeout exceeded")
	ErrInvalidRequest        = errors.New("invalid request")
	ErrReceiptsNotFound      = errors.New("receipts not found")
	ErrInvalidToken          = errors.New("invalid token")
	ErrExpiredToken          = errors.New("expired token")
)

func extractError(code int, msg any) error {
	switch code {
	case ServiceUnavailableCode:
		return ErrUbkServiceUnavailable

	case UnauthorizedCode:
		return Errunauthorized

	case InvalidRequestCode:
		return ErrInvalidRequest

	case ReceiptNotFoundCode:
		return ErrReceiptsNotFound

	case InvalidToken:
		return ErrInvalidToken

	case ExpiredToken, ExpiredTokenV2:
		return ErrExpiredToken

	default:
		return fmt.Errorf("ubk error response code: %d, message: %v", code, msg)
	}
}
