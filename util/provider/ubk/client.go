package ubk

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/bytedance/sonic"
	"github.com/google/uuid"
)

var json = sonic.ConfigDefault

const defaultTimeout = 60 * time.Second

type Client struct {
	username   string
	password   string
	baseUrl    string
	token      string
	accountNum string
	secretKey  []byte
	client     *http.Client
}

func NewClient(baseUrl, accountNum, username, password, secretKey string) *Client {
	c := &Client{
		baseUrl:    baseUrl + "/api/v1/jsonrpc",
		accountNum: accountNum,
		username:   username,
		password:   password,
		secretKey:  []byte(secretKey),
		client:     &http.Client{Timeout: defaultTimeout},
	}

	_ = c.refreshToken(context.Background())

	return c
}

func (c *Client) sendRequest(ctx context.Context, url string, request Request, method string) (resp Response, err error) {
	reqBody, err := json.Marshal(request)
	if err != nil {
		return resp, fmt.Errorf("failed to marshal request: %w", err)
	}

	digest, err := c.generateDigest(c.secretKey, reqBody)
	if err != nil {
		return resp, fmt.Errorf("failed to generate digest: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewReader(reqBody))
	if err != nil {
		return resp, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	req.Header.Set("Header-Login", c.username)
	req.Header.Set("Header-Sign", digest)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", c.token)

	result, err := c.client.Do(req)
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) {
			return resp, ErrTimeoutExceeded
		}
		return
	}
	defer result.Body.Close()

	err = json.NewDecoder(result.Body).Decode(&resp)
	if err != nil {
		err = fmt.Errorf("failed to unmarshal response: %v", err)
		return
	}

	if resp.Error != nil {
		err = extractError(resp.Error.Code, resp.Error.Message)
		if err != nil && errors.Is(err, ErrExpiredToken) {
			e := c.refreshToken(ctx)
			if e != nil {
				err = errors.Join(err, e)
				return
			}
			return
		}
	} else if result.StatusCode != http.StatusOK {
		var body []byte
		body, err = io.ReadAll(result.Body)
		if err != nil {
			err = fmt.Errorf("response not ok: %s, read body error: %v", result.Status, err)
			return
		}
		err = fmt.Errorf("response not ok: %s, body: %s", result.Status, string(body))
		return
	}

	return
}

func (c *Client) generateDigest(secret, requestBody []byte) (string, error) {
	hmacDigest := hmac.New(sha256.New, secret)
	_, err := hmacDigest.Write(requestBody)
	if err != nil {
		return "", err
	}

	digestBase64 := base64.StdEncoding.EncodeToString(hmacDigest.Sum(nil))

	return digestBase64, nil
}

func (c *Client) refreshToken(ctx context.Context) (err error) {
	req := Request{
		JsonRPC: "2.0",
		Id:      uuid.NewString(),
		Method:  "login",
		Params: Params{
			Username: c.username,
			Password: c.password,
		},
	}

	resp, err := c.sendRequest(ctx, c.baseUrl, req, http.MethodPost)
	if err != nil {
		return
	}

	if resp.Status {
		c.token = "Bearer " + resp.Result.AccessToken
	}

	return
}
