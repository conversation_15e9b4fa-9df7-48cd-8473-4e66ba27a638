package ubk

import (
	"context"
	"net/http"
	"strconv"

	"github.com/google/uuid"
)

func (c *Client) GetAccountBalance(ctx context.Context) (resp int, err error) {
	req := Request{
		JsonRPC: "2.0",
		Id:      uuid.NewString(),
		Method:  "account.balance",
		Params: Params{
			Account: c.accountNum,
		},
	}
	result, err := c.sendRequest(ctx, c.baseUrl, req, http.MethodPost)

	b, _ := strconv.ParseFloat(result.Result.ResponseBody.Balance, 64)
	resp = int(b)
	return
}
