package nats

import (
	"sync"

	"github.com/bytedance/sonic"
	"github.com/nats-io/nats.go"
)

var (
	json   = sonic.ConfigDefault
	once   sync.Once
	client *Client
)

func Get(url, token string) *Client {
	once.Do(func() {
		nc, err := nats.Connect(url, nats.Token(token))
		if err != nil {
			panic(err)
		}

		js, err := nc.JetStream(nats.PublishAsyncMaxPending(10000))
		if err != nil {
			panic(err)
		}

		client = &Client{
			js: js,
		}
	})

	return client
}

type Client struct {
	js nats.JetStreamContext
}

func (p *Client) Publish(channel, msgId string, data any) (err error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return
	}
	_, err = p.js.Publish(channel, jsonData, nats.MsgId(msgId))

	return
}

func (p *Client) Subscribe(channel, group string, handler func(data []byte) error) (err error) {
	_, err = p.js.QueueSubscribe(channel, group, func(m *nats.Msg) {
		e := handler(m.Data)
		if e != nil {
			return
		}
		_ = m.Ack()
	}, nats.Durable(group), nats.DeliverAll(), nats.ManualAck())

	return
}
