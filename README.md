# obihayot

[![Build Status](https://travis-ci.org/sevbo2003/obihayot.svg?branch=master)](https://travis-ci.org/sevbo2003/obihayot)
[![Built with](https://img.shields.io/badge/Built_with-Cookiecutter_Django_Rest-F7B633.svg)](https://github.com/agconti/cookiecutter-django-rest)

Obihayot crm project. Check out the project's [documentation](http://sevbo2003.github.io/obihayot/).

# Prerequisites

- [Docker](https://docs.docker.com/docker-for-mac/install/)  

# Local Development

Start the dev server for local development:
```bash
docker-compose up
```

Run a command inside the docker container:

```bash
docker-compose run --rm web [command]
```

# Click Merchant Hold API

This API allows you to manage the process of holding, confirming, cancelling, and checking the status of funds on a user's card via Click. Below you will find the available endpoints, their purposes, and example requests and responses.

---

## API Endpoints

### 1. Create Hold
- **Endpoint:** `POST /merchant/hold/create`
- **Description:** Создание холдирование средств с карты пользователя.
- **Request Body:**
  ```json
  {
    "service_id": 10121,
    "phone_number": "998971234567",
    "amount": 1000,
    "external_id": "11",
    "time": 60
  }
  ```
- **Success Response:**
  ```json
  {
    "status": 1,
    "message": "Денежные средства холдированы",
    "hold_id": 4598175625,
    "payment_id": 4131498653,
    "full_amount": 10000,
    "confirmed_amount": 0,
    "cancelled_amount": 0,
    "created_time": "2025-06-11T20:12:02.388Z",
    "completed_time": ""
  }
  ```
- **Error Response:**
  ```json
  {
    "error_code": 400,
    "error_note": "Bad request"
  }
  ```

---

### 2. Confirm Hold
- **Endpoint:** `POST /merchant/hold/confirm`
- **Description:** Подтверждение снятия холдированных средств. Сумма снятия может быть меньше чем сумма холдирования.
- **Request Body:**
  ```json
  {
    "amount": 1000,
    "payment_id": 4131498653
  }
  ```
- **Success Response:**
  ```json
  {
    "status": 2,
    "message": "Денежные средства списаны",
    "hold_id": 4598175625,
    "payment_id": 4131498653,
    "full_amount": 10000,
    "confirmed_amount": 7000,
    "cancelled_amount": 3000,
    "created_time": "2025-06-11T20:12:02.388Z",
    "completed_time": "2025-06-11T20:45:07.388Z"
  }
  ```
- **Error Response:**
  ```json
  {
    "error_code": 401,
    "error_note": "Not Authorized"
  }
  ```

---

### 3. Cancel Hold
- **Endpoint:** `POST /merchant/hold/cancel`
- **Description:** Возврат ранее холдированных средств пользователю.
- **Request Body:**
  ```json
  {
    "payment_id": 4131498653
  }
  ```
- **Success Response:**
  ```json
  {
    "status": 3,
    "message": "Денежные средства были отменены",
    "hold_id": 4598175625,
    "payment_id": 4131498653,
    "full_amount": 10000,
    "confirmed_amount": 0,
    "cancelled_amount": 10000,
    "created_time": "2025-06-11T20:12:02.388Z",
    "completed_time": "2025-06-11T20:45:07.388Z"
  }
  ```
- **Error Response:**
  ```json
  {
    "error_code": 401,
    "error_note": "Not authorized"
  }
  ```

---

### 4. Hold Status
- **Endpoint:** `GET /merchant/hold/status/{payment_id}`
- **Description:** Получение состояния холдирования по payment_id.
- **Success Response:**
  ```json
  {
    "status": 3,
    "message": "Денежные средства были отменены",
    "hold_id": 4598175625,
    "payment_id": 4131498653,
    "full_amount": 10000,
    "confirmed_amount": 0,
    "cancelled_amount": 10000,
    "created_time": "2025-06-11T20:12:02.388Z",
    "completed_time": "2025-06-11T20:45:07.388Z"
  }
  ```
- **Error Response:**
  ```json
  {
    "error_code": 409,
    "error_note": "Conflict"
  }
  ```

---

## Status Codes
- **0**: Запрос на холдирование не выполнен
- **1**: Денежные средства холдированы
- **2**: Денежные средства списаны
- **3**: Денежные средства были отменены
- **4**: Срок холдирования истек
- **5**: Ожидается создание платежа

---

## API Flow Diagram

```mermaid
flowchart TD
  A["/merchant/hold/create"] -->|"POST"| B["Создание холдирования средств с карты пользователя"]
  C["/merchant/hold/confirm"] -->|"POST"| D["Подтверждение снятия холдированных средств"]
  E["/merchant/hold/cancel"] -->|"POST"| F["Отмена холдирования, возврат средств"]
  G["/merchant/hold/status/{payment_id}"] -->|"GET"| H["Получение состояния холдирования по payment_id"]
  style A fill:#f9f,stroke:#333,stroke-width:2px
  style C fill:#bbf,stroke:#333,stroke-width:2px
  style E fill:#bfb,stroke:#333,stroke-width:2px
  style G fill:#ffb,stroke:#333,stroke-width:2px
```

---

## Error Codes
- **400**: Bad request (не предоставлен необходимые параметры)
- **401**: Not Authorized (не авторизован)
- **409**: Conflict (запрос дублирован)
- **500**: Internal server error (ошибка в обработке запроса)

---

## License
MIT
