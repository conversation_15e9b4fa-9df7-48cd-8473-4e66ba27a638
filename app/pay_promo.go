package app

import (
	"context"
	"errors"
	"fmt"
	"time"

	"billing_service/model"
	"billing_service/util/provider/payme"
)

func (a *App) PayPromoTask(ctx context.Context, req model.PayPromoRequest, attempt int) (err error) {
	if attempt > 30 {
		_, _, err = a.DriverPromoBalanceToBalance(ctx, req.DriverId, req.OrderId, req.Amount, req.Comment)
		return
	}

	if attempt <= 1 {
		time.Sleep(30 * time.Second)
	}

	card, err := a.repo.GetCardByUserId(ctx, req.DriverId, "driver")
	if err != nil {
		return
	}

	if card.Id == 0 {
		_, _, err = a.DriverPromoBalanceToBalance(ctx, req.DriverId, req.OrderId, req.Amount, req.Comment)
		return
	}

	_, err = a.payme.CardCheckLong(ctx, card.PaymeToken.String)
	if err != nil {
		switch err {
		case payme.ErrProcessingCenterUnavailable,
			payme.ErrTimeoutExceeded,
			payme.ErrUnknown:
			err = fmt.Errorf("check driver card: %v", err)
			return
		default:
			_, _, err = a.DriverPromoBalanceToBalance(ctx, req.DriverId, req.OrderId, req.Amount, req.Comment)
			return
		}
	}

	r := model.A2CPaymentRequest{
		OrderId:    req.OrderId,
		DriverId:   req.DriverId,
		CardId:     card.Id,
		CardNumber: card.FullNumber.String,
		Amount:     req.Amount,
		Reason:     model.PaymentReasonPayPromo,
	}

	switch {
	case a.repo.GetSysParam("is_ubk_enabled", "").Bool():
		if r.CardNumber == "" {
			err = errors.New("driver full card number not found for ubk")
			return
		}

		err = a.payA2CWithUbk(ctx, r, attempt)

	default:
		err = errors.New("all a2c providers disabled")
	}

	return
}
