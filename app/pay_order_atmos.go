package app

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"billing_service/model"
	"billing_service/util/provider/atmos"

	"github.com/google/uuid"
	null "github.com/guregu/null/v6"
	"github.com/riverqueue/river"
)

var (
	RetryCountForAtmos = 3
	MaxAtmosAttempts   = 12 // Maximum number of failed attempts before writing debt directly
)

func (a *App) PayOrderAtmosTask(ctx context.Context, req model.PayOrderRequest, attempt int) (err error) {
	var (
		isPaid         bool
		cancelWithDebt bool
	)

	defer func() {
		if cancelWithDebt || (!isPaid && attempt >= RetryCountForAtmos) {
			err = a.orderPayCancelWithDebt(ctx, req, false)
			return
		}
	}()

	if attempt > 1 {
		var status null.Int
		status, err = a.repo.GetClientOrderPaymentStatus(ctx, req.OrderId)
		if err != nil {
			return
		}

		if status.Int64 != 0 {
			isPaid = true
			return
		}

		payment, err := a.repo.GetPaymentStatusByOrderId(ctx, req.OrderId, model.PaymentReasonPayOrder)
		if err != nil {
			return fmt.Errorf("get payment status: %v", err)
		}

		if payment.IsFinished() {
			isPaid = true
			err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusFinished)
			if err != nil {
				return fmt.Errorf("update payment status: %v", err)
			}
			return a.repo.UpdateOrderClientPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
		}

		if payment.IsCreated() {
			transaction, err := a.atmos.GetTransaction(ctx, payment.Invoice)
			if err != nil {
				return fmt.Errorf("atmos get transaction status: %v", err)
			}

			switch {
			case transaction.Payload.IsSuccess():
				isPaid = true
				err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusFinished)
				if err != nil {
					return fmt.Errorf("update payment status: %v", err)
				}

				// We don't reset payment attempts on successful payments anymore
				// They are reset automatically once per month

				return a.repo.UpdateOrderClientPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
			case transaction.Payload.IsFailed():
				cancelWithDebt = true
				err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusCancelled)
				if err != nil {
					return fmt.Errorf("update payment status to cancelled: %v", err)
				}

				card, err := a.repo.GetCardById(ctx, int(req.CardId), req.ClientId, "client")
				if err == nil && card.AtmosHash.String != "" {
					if err := a.repo.IncrementCardPaymentAttempts(ctx, card.AtmosHash.String); err != nil {
						a.log.Errorf("Failed to increment card payment attempts: %v", err)
					}
				}

				return fmt.Errorf("atmos transaction failed: %s (status: %s)", transaction.Payload.ResultCode, transaction.Payload.GetTransactionStatus().String())
			case transaction.Payload.IsPending():
				if attempt < 3 {
					return river.JobSnooze(60 * time.Second)
				} else {
					return errors.New("pay in process")
				}
			default:
				if attempt < 3 {
					return river.JobSnooze(60 * time.Second)
				} else {
					return errors.New("pay in process")
				}
				// err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusCancelled)
				// if err != nil {
				// 	return err
				// }
			}
		}
	}

	card, err := a.repo.GetCardById(ctx, int(req.CardId), req.ClientId, "client")
	if err != nil {
		return fmt.Errorf("get client card: %v", err)
	}

	if card.AtmosToken.String == "" {
		cancelWithDebt = true
		return errors.New("client card atmos token not found")
	}

	if card.AtmosHash.String != "" {
		attemptCount, err := a.repo.GetCardPaymentAttempts(ctx, card.AtmosHash.String)
		if err != nil {
			a.log.Errorf("Failed to get card payment attempts: %v", err)
		} else if attemptCount >= MaxAtmosAttempts {
			a.log.Infof("Card with hash %s has exceeded maximum payment attempts (%d). Writing debt directly.",
				card.AtmosHash.String, attemptCount)
			cancelWithDebt = true
			return errors.New("card has exceeded maximum payment attempts")
		}
	}

	orderID := strconv.Itoa(req.OrderId)
	invoiceID := fmt.Sprintf("order_%s_%s", orderID, uuid.NewString())

	cardDetails, err := a.atmos.GetCardDetails(ctx, card.AtmosToken.String)
	if err != nil {
		cancelWithDebt = true
		return fmt.Errorf("get card details: %v", err)
	}

	holdReq := atmos.UklonHoldRequest{
		ExtID:        uuid.NewString(),
		Amount:       atmos.SoumToTiyin(req.TotalClientCost), // Convert UZS to tiyin (multiply by 100)
		StoreID:      strconv.Itoa(a.cfg.Atmos.StoreID),
		Account:      orderID,
		InvoiceID:    invoiceID,
		CardID:       cardDetails.Payload.Card.ID,
		ClientIPAddr: "127.0.0.1",
	}

	holdResp, err := a.atmos.UklonHold(ctx, holdReq)
	if err != nil {
		return fmt.Errorf("atmos hold request: %v", err)
	}

	if !holdResp.Payload.IsSuccess() {
		if card.AtmosHash.String != "" {
			if err := a.repo.IncrementCardPaymentAttempts(ctx, card.AtmosHash.String); err != nil {
				a.log.Errorf("Failed to increment card payment attempts: %v", err)
			}
		}
		cancelWithDebt = true
		return fmt.Errorf("atmos hold transaction not successful: %s (status: %s)", holdResp.Payload.ResultCode, holdResp.Payload.GetTransactionStatus().String())
	}

	transactionID := fmt.Sprintf("%d", holdResp.Payload.ID)
	payment := model.Payment{
		OrderId:    null.IntFrom(int64(req.OrderId)),
		UserId:     req.ClientId,
		UserType:   "client",
		CardId:     null.IntFrom(int64(req.CardId)),
		Amount:     req.TotalClientCost,
		Invoice:    transactionID,
		Status:     model.PaymentStatusCreated,
		ProviderId: model.ProviderIdAtmos,
		Reason:     model.PaymentReasonPayOrder,
	}

	err = a.repo.CreatePayment(ctx, payment)
	if err != nil {
		return fmt.Errorf("create payment: %v", err)
	}

	applyResp, err := a.atmos.UklonApply(ctx, transactionID)
	if err != nil {
		return fmt.Errorf("atmos apply request: %v", err)
	}

	switch {
	case applyResp.Payload.IsPending():
		return river.JobSnooze(60 * time.Second)

	case applyResp.Payload.IsFailed():
		if card.AtmosHash.String != "" {
			if err := a.repo.IncrementCardPaymentAttempts(ctx, card.AtmosHash.String); err != nil {
				a.log.Errorf("Failed to increment card payment attempts: %v", err)
			}
		}
		cancelWithDebt = true
		return fmt.Errorf("atmos apply transaction failed: %s (status: %s)", applyResp.Payload.ResultCode, applyResp.Payload.GetTransactionStatus().String())

	case !applyResp.Payload.IsSuccess():
		if card.AtmosHash.String != "" {
			if err := a.repo.IncrementCardPaymentAttempts(ctx, card.AtmosHash.String); err != nil {
				a.log.Errorf("Failed to increment card payment attempts: %v", err)
			}
		}
		cancelWithDebt = true
		return fmt.Errorf("atmos apply transaction not successful: %s (status: %s)", applyResp.Payload.ResultCode, applyResp.Payload.GetTransactionStatus().String())
	}

	isPaid = true

	err = a.repo.UpdatePaymentStatus(ctx, transactionID, model.PaymentStatusFinished)
	if err != nil {
		return fmt.Errorf("update payment status: %v", err)
	}

	err = a.repo.UpdateOrderClientPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
	if err != nil {
		return fmt.Errorf("update order payment status: %v", err)
	}

	// We don't reset payment attempts on successful payments anymore
	// They are reset automatically once per month

	return nil
}
