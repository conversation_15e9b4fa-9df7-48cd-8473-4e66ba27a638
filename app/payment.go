package app

import (
	"context"
	"errors"
	"fmt"

	"billing_service/model"
	"billing_service/util/provider/payme"
	"billing_service/util/provider/ubk"

	"github.com/google/uuid"
	null "github.com/guregu/null/v6"
)

func (a *App) checkAndUpdateClientPaymePayment(ctx context.Context, receiptId string) (receiptCreated, receiptPaid, inProcess bool, err error) {
	status, err := a.payme.GetReceiptStatus(ctx, receiptId)
	if err != nil {
		if err != payme.ErrReceiptsNotFound {
			err = fmt.Errorf("check receipt: %v", err)
			return
		}
	} else if status.Valid {
		switch status.Int64 {
		case payme.CheckStatusCreated:
			receiptCreated = true
			return
		case payme.CheckStatusTransactionCreation, payme.CheckStatusWithdrawing, payme.CheckStatusTransactionClosing, payme.CheckStatusTransactionClosingQueued:
			receiptCreated = true
			inProcess = true
			return
		case payme.CheckStatusPaid:
			receiptCreated = true
			receiptPaid = true
			err = a.repo.UpdatePaymentStatus(ctx, receiptId, model.PaymentStatusFinished)
			return

		case payme.CheckStatusCancelled:
			err = a.repo.UpdatePaymentStatus(ctx, receiptId, model.PaymentStatusCancelled)
			return
		}
	}

	if err != payme.ErrReceiptsNotFound {
		_, err = a.payme.CancelReceipt(ctx, receiptId)
		if err != nil && err != payme.ErrCheckAlreadyCancelled {
			a.log.Errorf("cancel payme receipt %s: %v", receiptId, err)
		}
	}

	err = a.repo.UpdatePaymentStatus(ctx, receiptId, model.PaymentStatusCancelled)

	return
}

// ResetAllCardPaymentAttemptsTask resets all card payment attempts
// This task is scheduled to run once a month
func (a *App) ResetAllCardPaymentAttemptsTask(ctx context.Context) error {
	a.log.Info("Starting monthly reset of all card payment attempts")

	count, err := a.repo.ResetAllCardPaymentAttempts(ctx)
	if err != nil {
		a.log.Errorf("Failed to reset card payment attempts: %v", err)
		return fmt.Errorf("failed to reset card payment attempts: %v", err)
	}

	a.log.Infof("Successfully reset %d card payment attempts", count)
	return nil
}

func (a *App) UbkPing(ctx context.Context) error {
	_, err := a.ubk.Ping(ctx, ubk.Params{
		Input: "ping",
	})
	if err != nil {
		return fmt.Errorf("ubk ping: %w", err)
	}

	return nil
}

func (a *App) GetClickSuperAppPaymentLink(ctx context.Context, orderId int) (link string, err error) {
	order, err := a.repo.GetOrderInfo(ctx, orderId)
	if err != nil {
		return
	}

	if order.TotalClientCost.Int64 == 0 {
		err = errors.New("total client cost is zero")
		return
	}

	invoice := uuid.New().String()

	payment := model.Payment{
		UserType:   "client",
		UserId:     order.ClientId,
		CardId:     order.ClientCardId,
		OrderId:    null.IntFrom(int64(orderId)),
		Amount:     int(order.TotalClientCost.Int64),
		Status:     model.PaymentStatusCreated,
		Reason:     model.PaymentReasonPayOrder,
		ProviderId: model.ProviderIdClick,
		Invoice:    invoice,
	}

	err = a.repo.CreatePayment(ctx, payment)
	if err != nil {
		return
	}

	p, err := a.repo.GetPaymentDetailsByInvoice(ctx, invoice)
	if err != nil {
		return
	}

	return a.click.GenerateSuperAppPayment(payment.Amount, p.Id, true, false, "https://mytaxi.uz"), nil
}
