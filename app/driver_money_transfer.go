package app

import (
	"context"
	"fmt"
	"time"

	"billing_service/model"
	"billing_service/util"
)

const (
	MinimalTransferAmount           = 3000
	MoneyTransactionStatusFinished  = 2
	MoneyTransferStatusCreated      = 1
	MoneyTransactionStatusCancelled = -1
	DeduplicationTTL                = 10 * time.Second
)

type moneyTransferData struct {
	Code       string `json:"code"`
	SenderID   int    `json:"sender_id"`
	ReceiverID int    `json:"receiver_id"`
	Amount     int    `json:"amount"`
}

type driverMoneyTransferCreate struct {
	TransactionId int64  `json:"transfer_id"`
	ReceiverName  string `json:"receiver_name"`
	Code          int    `json:"code"`
	Wait          int32  `json:"wait"`
}

// checkPerformMoneyTransferTransaction that's used for validationa anf restrictions
func (a *App) checkCreateMoneyTransferTransaction(ctx context.Context, sender, receiver, amount int) (errType string, err error) {
	key := fmt.Sprintf("create_money_transfer:%d:%d", sender, receiver)

	exists, err := a.redis.SetNX(ctx, key, true, DeduplicationTTL).Result()
	if err != nil {
		return "redis_error", fmt.Errorf("failed to set deduplication key: %v", err)
	}

	if !exists {
		return "transfer_money_limit", fmt.Errorf("Please wait before making another transfer")
	}

	if sender == receiver {
		return "data_validation", fmt.Errorf("You cannot send money to yourself")
	}

	receiverExists, err := a.repo.GetDriverExists(ctx, receiver)
	if err != nil {
		return "", fmt.Errorf("failed to get driver exists: %w", err)
	}

	if !receiverExists {
		return "driver_not_found", fmt.Errorf("receiver driver not found")
	}

	driverAccount, err := a.repo.GetDriverBalance(ctx, sender)
	if err != nil {
		return errType, fmt.Errorf("failed to get driver balance: %v", err)
	}

	if amount < MinimalTransferAmount {
		return "transfer_money_limit", fmt.Errorf("minimal amount should be %v", MinimalTransferAmount)
	}

	if driverAccount.Balance < amount {
		return "transfer_money_limit", fmt.Errorf("insufficient balance")
	}

	if driverAccount.Balance-amount < MinimalTransferAmount {
		return "transfer_money_limit", fmt.Errorf("this amount cannot be transferred. Some money should remain on the account")
	}

	return
}

func (a *App) DriverMoneyTransferCreate(ctx context.Context, sender, receiver, amount int, lang string) (result driverMoneyTransferCreate, errType string, err error) {
	errType, err = a.checkCreateMoneyTransferTransaction(ctx, sender, receiver, amount)
	if err != nil {
		return
	}

	transactionId, err := a.repo.DriverMoneyTransferCreate(ctx, sender, receiver, amount)
	if err != nil {
		err = fmt.Errorf("failed to create money transfer: %v", err)
		return
	}

	driverInfo, err := a.repo.GetDriverInfo(ctx, sender)
	if err != nil {
		err = fmt.Errorf("failed to get driver info: %v", err)
		return
	}

	code := util.GenerateSMSCode()

	err = a.repo.SendSMS(driverInfo.Phone, fmt.Sprintf(model.MessageSmsCode[lang], code))
	if err != nil {
		err = fmt.Errorf("failed to send SMS: %v", err)
		return
	}

	data := moneyTransferData{
		Code:       code,
		SenderID:   sender,
		ReceiverID: receiver,
		Amount:     amount,
	}

	dataBytes, err := json.Marshal(data)
	if err != nil {
		err = fmt.Errorf("failed to marshal data: %v", err)
		return
	}

	key := fmt.Sprintf("money_transfer_data:%d", transactionId)

	err = a.redis.Set(a.ctx, key, dataBytes, 2*time.Minute).Err()
	if err != nil {
		err = fmt.Errorf("redis set: %v", err)
		return
	}

	receiverInfo, err := a.repo.GetDriverInfo(ctx, receiver)
	if err != nil {
		err = fmt.Errorf("failed to get receiver info: %v", err)
		return
	}

	result.TransactionId = transactionId
	result.Code = util.ParseInt(code)
	result.ReceiverName = receiverInfo.Name
	result.Wait = 120
	return
}

func (a *App) checkConfirmMoneyTransferTransaction(ctx context.Context, driverId, transactionId, code int) (resp moneyTransferData, errType string, err error) {
	key := fmt.Sprintf("confirm_transfer_money:transaction_id:%d:%d", transactionId, code)

	exists, err := a.redis.SetNX(ctx, key, true, DeduplicationTTL).Result()
	if err != nil {
		err = fmt.Errorf("failed to set deduplication key: %v", err)
		return
	}
	if !exists {
		errType = "transfer_money_limit"
		err = fmt.Errorf("failed to set deduplication key: %v", err)
		return
	}

	resp, errType, err = a.getTransferInfo(ctx, transactionId)
	if err != nil {
		errType = "sms_code_expired"
		err = fmt.Errorf("sms code expired")
		return
	}

	if errType != "" {
		return
	}

	if driverId != resp.SenderID {
		errType = "driver_id_incorrect"
		err = fmt.Errorf("driver id %d is incorrect", driverId)
		return
	}

	if util.ParseInt(resp.Code) != code {
		errType = "permission_denied"
		err = fmt.Errorf("invalid code")
		return
	}

	driverAccount, err := a.repo.GetDriverBalance(ctx, driverId)
	if err != nil {
		return
	}

	if driverAccount.Balance < resp.Amount {
		errType = "transfer_money_limit"
		err = fmt.Errorf("insufficient balance")
		return
	}

	if driverAccount.Balance-resp.Amount < MinimalTransferAmount {
		errType = "transfer_money_limit"
		err = fmt.Errorf("this amount cannot be transferred; some money should remain on the account")
		return
	}

	status, err := a.repo.GetDriverMoneyTransferStatus(ctx, transactionId)
	if err != nil {
		return
	}

	if status != MoneyTransferStatusCreated {
		err = fmt.Errorf("transaction status is not in created state")
		return
	}

	return
}

func (a *App) DriverMoneyTransferConfirm(ctx context.Context, driverId, transactionId, code int) (errType string, err error) {
	transferInfo, errType, err := a.checkConfirmMoneyTransferTransaction(ctx, driverId, transactionId, code)
	if err != nil {
		return
	}

	err = a.repo.DriverTransferToDriver(ctx, transferInfo.SenderID, transferInfo.ReceiverID, transferInfo.Amount)
	if err != nil {
		e := a.repo.UpdateDriverMoneyTransferTransactionStatus(ctx, transactionId, MoneyTransactionStatusCancelled)
		if e != nil {
			err = fmt.Errorf("failed to update transaction status: %v", e)
		} else {
			err = fmt.Errorf("failed to confirm transaction: %v", err)
		}
		errType = "transfer_failed"
		return
	}

	err = a.repo.UpdateDriverMoneyTransferTransactionStatus(ctx, transactionId, MoneyTransactionStatusFinished)
	if err != nil {
		errType = "transfer_failed"
		err = fmt.Errorf("failed to update transaction status: %v", err)
	}

	return
}

func (a *App) getTransferInfo(ctx context.Context, transactionId int) (data moneyTransferData, errType string, err error) {
	rawData, err := a.redis.Get(ctx, fmt.Sprintf("money_transfer_data:%d", transactionId)).Result()
	if err != nil {
		err = fmt.Errorf("failed to get transfer data: %v", err)
		return
	}

	if rawData == "" {
		errType = "sms_code_expired"
		err = fmt.Errorf("no transfer data found for transaction ID: %d", transactionId)
		return
	}

	err = json.Unmarshal([]byte(rawData), &data)

	return
}

func (a *App) GetDriverFallbackLinks(ctx context.Context, driverId int) (resp []model.DriverPaymentFallback, err error) {
	if a.repo.GetSysParam("is_payme_fallback_enabled", "0").Bool() {
		resp = append(resp, model.DriverPaymentFallback{
			Provider: "payme",
			Link:     fmt.Sprintf("%s&driver_id=%d", a.cfg.Payme.FallBackUrl, driverId),
		})
	}

	if a.repo.GetSysParam("is_paynet_fallback_enabled", "0").Bool() {
		resp = append(resp, model.DriverPaymentFallback{
			Provider: "paynet",
			Link:     fmt.Sprintf("%s&c=%d", a.cfg.Paynet.FallBackUrl, driverId),
		})
	}

	return
}
