package app

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"billing_service/model"
	"billing_service/util/provider/payme"

	null "github.com/guregu/null/v6"
)

var cardCheckTimeout = 24 * time.Hour

func (a *App) GetCards(ctx context.Context, userId int, userType string) (resp []model.Card, err error) {
	resp, err = a.repo.GetCardsByUserId(ctx, userId, userType)
	if err != nil {
		return
	}

	paymeEnabled := a.repo.GetSysParam("is_payme_enabled", "0").Bool()
	atmosEnabled := a.repo.GetSysParam("is_atmos_enabled", "0").Bool()

	l := len(resp)

	if l == 0 {
		return
	}

	if l == 1 {
		card := resp[0]
		if card.PaymeToken.String != "" {
			if !paymeEnabled {
				resp[0].Status = "fail"
				return
			}
			if card.CheckedAt.Valid && time.Since(card.CheckedAt.Time) < cardCheckTimeout {
				resp[0].Status = "success"
				return
			}
			check, err := a.payme.CardCheck(ctx, card.PaymeToken.String)
			if err != nil {
				switch err {
				case payme.ErrCardNotFound,
					payme.ErrCardExpired,
					payme.ErrInvalidTokenFormat:
					resp[0].Status = "error"
				default:
					resp[0].Status = "fail"
				}
			} else if check.Result.Card.Verify {
				resp[0].Status = "success"
				err = a.repo.UpdateCardCheckedTime(ctx, card.Id)
				if err != nil {
					a.log.Errorf("update card %d checked time: %v", card.Id, err)
				}
			} else {
				resp[0].Status = "fail"
			}
		} else if card.AtmosToken.String != "" {
			if !atmosEnabled {
				resp[0].Status = "fail"
				return
			}
			if card.CheckedAt.Valid && time.Since(card.CheckedAt.Time) < cardCheckTimeout {
				resp[0].Status = "success"
				return
			}
			cardDetails, err := a.atmos.GetCardDetails(ctx, card.AtmosToken.String)
			if err != nil {
				a.log.Errorf("failed to get atmos card details: %v", err)
				resp[0].Status = "fail"
			} else if cardDetails.Payload.Card.Status && cardDetails.Payload.Card.Approved {
				resp[0].Status = "success"
				err = a.repo.UpdateCardCheckedTime(ctx, card.Id)
				if err != nil {
					a.log.Errorf("update card %d checked time: %v", card.Id, err)
				}
			} else {
				resp[0].Status = "fail"
			}
		} else {
			resp[0].Status = "error"
		}

		return
	}

	var wg sync.WaitGroup

	for i, card := range resp {
		if card.PaymeToken.String != "" {
			if !paymeEnabled {
				resp[i].Status = "fail"
				continue
			}

			if card.CheckedAt.Valid && time.Since(card.CheckedAt.Time) < cardCheckTimeout {
				resp[i].Status = "success"
				continue
			}

			wg.Add(1)
			go func(i int, token string) {
				check, err := a.payme.CardCheck(ctx, token)
				if err != nil {
					switch err {
					case payme.ErrCardNotFound,
						payme.ErrCardExpired,
						payme.ErrInvalidTokenFormat:
						resp[i].Status = "error"
					default:
						resp[i].Status = "fail"
					}
				} else if check.Result.Card.Verify {
					resp[i].Status = "success"
					err = a.repo.UpdateCardCheckedTime(ctx, card.Id)
					if err != nil {
						a.log.Errorf("update card %d checked time: %v", card.Id, err)
					}
				} else {
					resp[i].Status = "fail"
				}
				wg.Done()
			}(i, card.PaymeToken.String)

		} else if card.AtmosToken.String != "" {
			if !atmosEnabled {
				resp[i].Status = "fail"
				continue
			}

			if card.CheckedAt.Valid && time.Since(card.CheckedAt.Time) < cardCheckTimeout {
				resp[i].Status = "success"
				continue
			}

			wg.Add(1)
			go func(i int, token string) {
				cardDetails, err := a.atmos.GetCardDetails(ctx, token)
				if err != nil {
					a.log.Errorf("failed to get atmos card details: %v", err)
					resp[i].Status = "fail"
				} else if cardDetails.Payload.Card.Status && cardDetails.Payload.Card.Approved {
					resp[i].Status = "success"
					err = a.repo.UpdateCardCheckedTime(ctx, card.Id)
					if err != nil {
						a.log.Errorf("update card %d checked time: %v", card.Id, err)
					}
				} else {
					resp[i].Status = "fail"
				}
				wg.Done()
			}(i, card.AtmosToken.String)

		} else {
			resp[i].Status = "error"
		}
	}

	wg.Wait()

	return
}

func (a *App) GetClientPaymeCards(ctx context.Context, clientId int) (resp []model.Card, err error) {
	resp, err = a.repo.GetCardsByUserId(ctx, clientId, "client")
	if err != nil {
		return
	}

	paymeEnabled := a.repo.GetSysParam("is_payme_enabled", "0").Bool()
	isPaymeTimeout := false

	for i, card := range resp {
		if card.PaymeToken.String == "" {
			continue
		}
		if !paymeEnabled || isPaymeTimeout {
			resp[i].Status = "fail"
			continue
		}

		if card.PaymeToken.String == "" {
			resp[i].Status = "error"
			continue
		}

		check, err := a.payme.CardCheck(ctx, card.PaymeToken.String)
		if err != nil {
			switch err {
			case payme.ErrCardNotFound,
				payme.ErrCardExpired,
				payme.ErrInvalidTokenFormat:
				resp[i].Status = "error"
			default:
				isPaymeTimeout = true
				resp[i].Status = "fail"
			}
		} else if check.Result.Card.Verify {
			resp[i].Status = "success"
		} else {
			resp[i].Status = "fail"
		}
	}

	return
}

func (a *App) GetAtmosCards(ctx context.Context, userId int, userType string) (resp []model.Card, err error) {
	cards, err := a.repo.GetCardsByUserId(ctx, userId, userType)
	if err != nil {
		return
	}

	atmosEnabled := a.repo.GetSysParam("is_atmos_enabled", "0").Bool()

	for _, card := range cards {
		var status string
		if card.AtmosToken.String != "" {
			if !atmosEnabled {
				status = "fail"
			} else {
				status = "success"
				cardDetails, err := a.atmos.GetCardDetails(ctx, card.AtmosToken.String)
				if err != nil {
					a.log.Errorf("failed to get atmos card details: %v", err)
					status = "fail"
				} else if !cardDetails.Payload.Card.Status {
					status = "fail"
				}
			}
		} else {
			status = "error"
		}

		resp = append(resp, model.Card{
			Id:     card.Id,
			Number: card.Number,
			Expire: card.Expire,
			Status: status,
			Brand:  card.Brand,
		})
	}

	return
}

func (a *App) AddClientPaymeCard(ctx context.Context, clientId int, token string) (errType string, err error) {
	check, err := a.payme.CardCheck(ctx, token)
	if err != nil {
		switch {
		case err == payme.ErrCardNotFound, err == payme.ErrInvalidTokenFormat:
			errType = "card_is_not_valid"
		case err == payme.ErrCardExpired:
			errType = "card_is_expired"
		default:
			errType = "processing_error"
		}
		return
	} else if !check.Result.Card.Verify {
		errType = "card_is_not_valid"
		err = errors.New("card is not valid")
		return
	}

	card := model.Card{
		PaymeToken: null.StringFrom(token),
		Number:     check.Result.Card.Number,
		Expire:     null.StringFrom(check.Result.Card.Expire),
	}

	if check.Result.Card.NumberHash != "" {
		card.PaymeHash = null.StringFrom(check.Result.Card.NumberHash)
	}

	card.Brand, card.Category, err = a.getCardBrand(ctx, card.Number)
	if err != nil {
		return
	}

	err = a.repo.AddPaymeCard(ctx, clientId, "client", card)

	return
}

func (a *App) DeleteClientPaymeCard(ctx context.Context, clientId, cardId int) (errType string, err error) {
	card, err := a.repo.GetCardById(ctx, cardId, clientId, "client")
	if err != nil || card.Id == 0 {
		return
	}

	orderExists, err := a.repo.GetClientActiveOrdersByCardExists(ctx, clientId, cardId)
	if err != nil {
		return
	}

	if orderExists {
		errType = "client_has_active_order_by_card"
		err = errors.New("You have an active order created with this card")
		return
	}

	success, err := a.payme.CardRemove(ctx, card.PaymeToken.String)
	if err != nil {
		switch err {
		case payme.ErrCardNotFound,
			payme.ErrInvalidTokenFormat:
			err = a.repo.DeleteCard(ctx, cardId, clientId, "client")
		}
		return
	}

	if success {
		err = a.repo.DeleteCard(ctx, card.Id, clientId, "client")
		if err != nil {
			return
		}
	}

	return
}

func (a *App) AddAtmosCard(ctx context.Context, userId int, userType string) (resp model.GetCardTokenResponse, err error) {
	// Note: Provider status check is now done in the handler
	// This method assumes the provider is enabled

	result, err := a.atmos.CardAdd(ctx, userId)
	if err != nil {
		a.log.Errorf("Failed to add Atmos card: %v", err)
		return resp, err
	}

	reqId := fmt.Sprintf("atmos_card_add:%d", result.PaymentID)
	err = a.redis.Set(ctx, reqId, fmt.Sprintf("%d:%s", userId, userType), 24*time.Hour).Err()
	if err != nil {
		a.log.Errorf("Failed to set Redis key: %v", err)
	}

	resp.PaymentUrl = result.URL
	return
}

func (a *App) DeleteCard(ctx context.Context, cardId int, userId int, userType string) (errType string, err error) {
	orderExists, err := a.repo.GetClientActiveOrdersByCardExists(ctx, userId, cardId)
	if err != nil {
		return
	}

	if orderExists {
		errType = "client_has_active_order_by_card"
		err = errors.New("You have an active order created with this card")
		return
	}

	err = a.repo.DeleteCard(ctx, cardId, userId, userType)

	return
}

func (a *App) GetClientCardSolvency(ctx context.Context, clientId, cardId, amount int) (errType string, err error) {
	debts, err := a.repo.GetDebts(ctx, clientId, "client")
	if err != nil {
		return
	}

	if len(debts) > 0 {
		errType = "client_has_debt"
		err = errors.New("You cannot use selected payment type due to the dept for previous order")
		return
	}

	card, err := a.repo.GetCardById(ctx, cardId, clientId, "client")
	if err != nil {
		return
	}

	if card.Id == 0 {
		errType = "client_has_no_payme_card"
		err = errors.New("card not found")
		return
	}

	balanceEnough, err := a.payme.CardBalance(ctx, card.PaymeToken.String, amount)
	if err != nil {
		switch err {
		case payme.ErrInsufficientBalance:
			errType = "card_insufficient_balance"
		case payme.ErrCardNotFound, payme.ErrInvalidTokenFormat:
			errType = "card_is_not_valid"
		case payme.ErrCardExpired:
			errType = "card_is_expired"
		default:
			errType = "processing_center_not_available"
		}
		return
	}

	if !balanceEnough {
		errType = "card_insufficient_balance"
		err = errors.New("card has insufficient balance")
	}

	return
}

func (a *App) GetOrderCardBalance(ctx context.Context, orderId, amount int) (resp model.ClientCardBalance, err error) {
	// key := "order_chosen_card:" + strconv.Itoa(orderId)
	// cardId, err := a.redis.Get(ctx, key).Int()
	// if err != nil && err != redis.Nil {
	// 	a.log.Errorf("redis get: %v", err)
	// }

	// if cardId == 0 {
	clientCard, err := a.repo.GetOrderChosenPaymeCardId(ctx, orderId)
	if err != nil || clientCard.CardId == 0 {
		return
	}
	// }

	card, err := a.repo.GetCardById(ctx, clientCard.CardId, clientCard.ClientId, "client")
	if err != nil || card.Id == 0 {
		return
	}

	resp.IsEnough, err = a.payme.CardBalance(ctx, card.PaymeToken.String, amount)
	if err != nil {
		switch err {
		case payme.ErrInsufficientBalance,
			payme.ErrCardNotFound,
			payme.ErrInvalidTokenFormat,
			payme.ErrCardExpired,
			payme.ErrUnknown:
		default:
			resp.IsEnough = true
		}
		err = nil
		return
	}

	return
}

func (a *App) getCardBrand(ctx context.Context, number string) (brand string, category null.String, err error) {
	if len(number) >= 6 {
		bin := number[:6]

		switch bin[:4] {
		case "8600":
			brand = "uzcard"
			return
		case "9860":
			brand = "humo"
			return
		}

		brand, category, err = a.repo.GetCardBrand(ctx, bin)
		if err != nil {
			err = fmt.Errorf("get card brand: %v", err)
			return
		} else if brand != "" {
			return
		}
	}

	if len(number) >= 1 {
		switch number[:1] {
		case "8":
			brand = "uzcard"
		case "9":
			brand = "humo"
		case "4":
			brand = "visa"
		case "5":
			brand = "mastercard"
		case "6":
			brand = "unionpay"
		}
	}

	return
}
