package app

import (
	"context"
	"errors"
	"fmt"
	"time"

	"billing_service/model"
	"billing_service/util/provider/payme"

	null "github.com/guregu/null/v6"
	"github.com/riverqueue/river"
)

func (a *App) PayOrderPaymeTask(ctx context.Context, req model.PayOrderRequest, attempt int) (err error) {
	var (
		receiptId       string
		receiptCreated  bool
		isPaid          bool
		inProcess       bool
		cancelWithDebt  bool
		driverCardExist bool
		driverCard      model.Card
	)

	defer func() {
		if cancelWithDebt || (!isPaid && attempt >= a.cfg.Defaults.P2PRetriesToCancelWithDebt) {
			err = a.orderPayCancelWithDebt(ctx, req, req.PayToDriverCard)
			return
		}
	}()

	if attempt > 1 {
		if attempt == 4 {
			a.repo.SendDriverNotification(req.DriverId, req.OrderId, req.TotalClientCost, fmt.Sprintf("driver_order:%d:paid_in_queue", req.OrderId), "driver_order_paid_in_queue")
		}

		var status null.Int
		status, err = a.repo.GetClientOrderPaymentStatus(ctx, req.OrderId)
		if err != nil {
			return
		}

		if status.Int64 != 0 {
			isPaid = true
		} else {
			var payment model.Payment
			payment, err = a.repo.GetPaymentStatusByOrderId(ctx, req.OrderId, model.PaymentReasonPayOrder)
			if err != nil {
				return
			}

			if payment.IsFinished() {
				isPaid = true
				if payment.IsP2p {
					driverCardExist = true
				}
			} else if payment.IsCreated() {
				receiptId = payment.Invoice
				receiptCreated, inProcess, isPaid, err = a.checkAndUpdateClientPaymePayment(ctx, receiptId)
				if err != nil {
					return
				}
				if inProcess {
					if attempt < 3 {
						err = river.JobSnooze(60 * time.Second) // pay in process
					} else {
						err = errors.New("pay in process")
					}
					return
				}
				if (receiptCreated || isPaid) && payment.IsP2p {
					driverCardExist = true
				}
			}
		}
	}

	if !isPaid {
		var clientCard model.Card
		clientCard, err = a.repo.GetCardById(ctx, req.CardId, req.ClientId, "client")
		if err != nil {
			return
		}

		if clientCard.Id == 0 {
			cancelWithDebt = true
			return
		}

		var balanceEnough bool
		balanceEnough, err = a.payme.CardBalanceLong(ctx, clientCard.PaymeToken.String, req.TotalClientCost)
		if err != nil {
			switch err {
			case payme.ErrInsufficientBalance,
				payme.ErrCardNotFound,
				payme.ErrInvalidTokenFormat,
				payme.ErrCardExpired:
				cancelWithDebt = true
				return
			default:
				err = fmt.Errorf("check card balance: %v", err)
			}
			return
		}

		if !balanceEnough {
			cancelWithDebt = true
			return
		}

		if !receiptCreated {

			if req.PayToDriverCard {
				driverCard, err = a.repo.GetCardByUserId(ctx, req.DriverId, "driver")
				if err != nil {
					return
				}

				if driverCard.Id > 0 {
					var check payme.Response
					check, err = a.payme.CardCheckLong(ctx, driverCard.PaymeToken.String)
					if err != nil {
						switch err {
						case payme.ErrCardNotFound,
							payme.ErrInvalidTokenFormat,
							payme.ErrCardExpired:
							a.log.Errorf("driver %d card %d check: %v", req.DriverId, driverCard.Id, err)
						default:
							err = fmt.Errorf("driver card check: %v", err)
							return
						}
					} else {
						driverCardExist = check.Result.Card.Verify
					}
				}
			}

			if driverCardExist {
				receiptId, err = a.payme.CreateReceiptP2P(ctx, req.OrderId, req.TotalClientCost, driverCard.PaymeToken.String, req.Comment)
				if err != nil {
					switch err {
					case payme.ErrCardNotFound,
						payme.ErrCardExpired,
						payme.ErrInvalidTokenFormat:
						driverCardExist = false
					default:
						err = fmt.Errorf("create p2p receipt: %v", err)
						return
					}
				} else {
					payment := model.Payment{
						UserId:     req.ClientId,
						UserType:   "client",
						OrderId:    null.IntFrom(int64(req.OrderId)),
						CardId:     null.IntFrom(int64(clientCard.Id)),
						Amount:     req.TotalClientCost,
						Status:     model.PaymentStatusCreated,
						Reason:     model.PaymentReasonPayOrder,
						Invoice:    receiptId,
						ProviderId: model.ProviderIdPayme,
						IsP2p:      true,
					}
					err = a.repo.CreatePayment(ctx, payment)
					if err != nil {
						return
					}
				}
			}

			if !driverCardExist {
				receiptId, err = a.payme.CreateReceipt(ctx, req.ClientId, req.OrderId, clientCard.Id, req.TotalClientCost, "client_order", req.Comment)
				if err != nil {
					err = fmt.Errorf("create merchant receipt: %v", err)
					return
				}
			}
		}

		var state null.Int
		state, err = a.payme.PayReceipt(ctx, clientCard.PaymeToken.String, receiptId)
		if err != nil {
			switch err {
			case payme.ErrInsufficientBalance,
				payme.ErrCardNotFound,
				payme.ErrCardExpired,
				payme.ErrInvalidTokenFormat:
				err = a.repo.UpdatePaymentStatus(ctx, receiptId, model.PaymentStatusCancelled)
				cancelWithDebt = true
				return
			case payme.ErrP2PIdenticalCards:
				err = a.repo.UpdatePaymentStatus(ctx, receiptId, model.PaymentStatusCancelled)
				if err != nil {
					return
				}
				err = a.repo.UpdateOrderPaymentStatusToSuccess(ctx, req.OrderId)
				return
			case payme.ErrReceiptsAlreadyPayed:
				err = nil
			default:
				err = fmt.Errorf("pay receipt %s: %v", receiptId, err)
				return
			}
		} else if state.Int64 != payme.CheckStatusPaid {
			err = fmt.Errorf("pay check state not success: %d", state.Int64)
			return
		}

		err = a.repo.UpdatePaymentStatus(ctx, receiptId, model.PaymentStatusFinished)
		if err != nil {
			return
		}
	}

	msgId := fmt.Sprintf("order:%d:paid", req.OrderId)
	a.repo.SendClientNotification(req.ClientId, req.OrderId, req.TotalClientCost, "client_"+msgId, "client_order_paid")

	if req.PayToDriverCard {
		if driverCardExist {
			err = a.RefillDriverBalance(ctx, req.DriverId, payme.GetPaymeCommissionPrice(req.TotalClientCost), 0, 0, req.Comment, msgId)
			if err != nil {
				a.log.Errorf("refill driver %d balance for payme p2p compensation: %v", req.DriverId, err)
			}

			a.repo.SendDriverNotification(req.DriverId, req.OrderId, req.TotalClientCost, "driver_"+msgId, "driver_order_paid_to_card")

			err = a.repo.UpdateOrderPaymentStatusToSuccess(ctx, req.OrderId)
			if err != nil {
				return
			}
		} else {
			r := model.A2CPaymentRequest{
				OrderId:  req.OrderId,
				DriverId: req.DriverId,
				Amount:   req.TotalClientCost,
				Reason:   model.PaymentReasonPayOrderForDriver,
				Comment:  req.Comment,
			}
			_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: r}, nil)
			if err != nil {
				return
			}
			err = a.repo.UpdateOrderClientPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
		}
	} else {
		err = a.repo.UpdateOrderClientPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
	}

	return
}
