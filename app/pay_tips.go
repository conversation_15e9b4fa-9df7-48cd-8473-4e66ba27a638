package app

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"billing_service/model"
	"billing_service/util/provider/payme"

	null "github.com/guregu/null/v6"
	"github.com/riverqueue/river"
)

func (a *App) PayTips(ctx context.Context, req model.PayTipsRequest) (err error) {
	card, err := a.repo.GetCardById(ctx, req.CardId, req.ClientId, "client")
	if err != nil {
		return
	}

	if card.Id == 0 {
		return fmt.Errorf("client card not found")
	}

	if card.PaymeToken.String != "" {
		req.CardToken = card.PaymeToken.String
		_, err = a.river.Insert(ctx, PayTipsArgs{PayTipsRequest: req}, nil)
	} else if card.AtmosToken.String != "" && a.repo.GetSysParam("is_atmos_enabled", "0").Bool() {
		req.CardToken = card.AtmosToken.String
		_, err = a.river.Insert(ctx, PayTipsAtmosArgs{PayTipsRequest: req}, nil)
	} else {
		err = fmt.Errorf("no valid payment token found for card")
	}

	return
}

func (a *App) PayTipsTask(ctx context.Context, req model.PayTipsRequest, attempt int) (err error) {
	var (
		receiptId       string
		receiptCreated  bool
		receiptPaid     bool
		inProcess       bool
		driverCardExist bool
		comment         = "Чаевые за заказ " + strconv.Itoa(req.OrderId)
	)

	if attempt > 1 {
		var payment model.Payment
		payment, err = a.repo.GetPaymentStatusByOrderId(ctx, req.OrderId, model.PaymentReasonPayTips)
		if err != nil || payment.IsFinished() {
			return
		}

		if payment.IsCreated() {
			receiptId = payment.Invoice
			receiptCreated, inProcess, receiptPaid, err = a.checkAndUpdateClientPaymePayment(ctx, receiptId)
			if err != nil {
				return
			}
			if inProcess {
				if attempt < 3 {
					err = river.JobSnooze(60 * time.Second) // pay in process
				} else {
					err = errors.New("pay in process")
				}
				return
			}
			if (receiptCreated || receiptPaid) && payment.IsP2p {
				driverCardExist = true
			}
		}
	}

	if req.DriverId == 0 {
		req.DriverId, err = a.repo.GetOrderDriverId(ctx, req.OrderId)
		if err != nil {
			return
		}
		if req.DriverId == 0 {
			return
		}
	}

	if !receiptPaid {
		var balanceEnough bool
		balanceEnough, err = a.payme.CardBalanceLong(ctx, req.CardToken, req.Amount)
		if err != nil {
			switch err {
			case payme.ErrProcessingCenterUnavailable,
				payme.ErrTimeoutExceeded:
				err = fmt.Errorf("check client %d card %d: %v", req.ClientId, req.CardId, err)
				return
			default:
				err = nil
			}
			return
		}

		if !balanceEnough {
			return
		}

		if !receiptCreated {

			var driverCard model.Card
			driverCard, err = a.repo.GetCardByUserId(ctx, req.DriverId, "driver")
			if err != nil {
				return
			}

			if driverCard.Id > 0 {
				var check payme.Response
				check, err = a.payme.CardCheck(ctx, driverCard.PaymeToken.String)
				if err != nil {
					switch err {
					case payme.ErrProcessingCenterUnavailable,
						payme.ErrTimeoutExceeded:
						err = fmt.Errorf("check driver %d card %d: %v", req.DriverId, driverCard.Id, err)
						return
					default:
						a.log.Errorf("check driver %d card %d: %v", req.DriverId, driverCard.Id, err)
					}
				} else {
					driverCardExist = check.Result.Card.Verify
				}
			}

			if driverCardExist {
				receiptId, err = a.payme.CreateReceiptP2P(ctx, req.OrderId, req.Amount, driverCard.PaymeToken.String, comment)
				if err != nil {
					switch err {
					case payme.ErrCardNotFound,
						payme.ErrCardExpired,
						payme.ErrInvalidTokenFormat:
						driverCardExist = false
					default:
						err = fmt.Errorf("create p2p receipt: %v", err)
						return
					}
				} else {
					payment := model.Payment{
						UserId:     req.ClientId,
						UserType:   "client",
						OrderId:    null.IntFrom(int64(req.OrderId)),
						CardId:     null.IntFrom(int64(req.CardId)),
						Amount:     req.Amount,
						Status:     model.PaymentStatusCreated,
						Reason:     model.PaymentReasonPayTips,
						Invoice:    receiptId,
						ProviderId: model.ProviderIdPayme,
						IsP2p:      true,
					}
					err = a.repo.CreatePayment(ctx, payment)
					if err != nil {
						return
					}
				}
			}

			if !driverCardExist {
				receiptId, err = a.payme.CreateReceipt(ctx, req.ClientId, req.OrderId, req.CardId, req.Amount, "client_tips", comment)
				if err != nil {
					return
				}
			}
		}

		var state null.Int
		state, err = a.payme.PayReceipt(ctx, req.CardToken, receiptId)
		if err != nil {
			switch err {
			case payme.ErrProcessingCenterUnavailable,
				payme.ErrTimeoutExceeded:
				err = fmt.Errorf("pay receipt %s: %v", receiptId, err)
				return
			case payme.ErrReceiptsAlreadyPayed:
				break
			default:
				err = nil
				return
			}
		} else if state.Int64 != payme.CheckStatusPaid {
			err = fmt.Errorf("pay check state not success: %d", state.Int64)
			return
		}

		err = a.repo.UpdatePaymentStatus(ctx, receiptId, model.PaymentStatusFinished)
		if err != nil {
			return
		}
	}

	if !driverCardExist {
		// if the driver card does not exist, refill driver balance
		err = a.RefillDriverBalance(ctx, req.DriverId, req.Amount, 0, 0, comment, comment)
		if err != nil {
			return
		}
	} else {
		err = a.RefillDriverBalance(ctx, req.DriverId, payme.GetPaymeCommissionPrice(req.Amount), 0, 0, comment, comment)
		if err != nil {
			a.log.Errorf("refill driver %d balance for payme p2p compensation: %v", req.DriverId, err)
			err = nil
		}
	}

	// send push to driver app via socket

	if attempt <= 5 {
		push := model.DriverSocketNotification{
			DriverId: req.DriverId,
			Body: model.DriverSocketNotificationBody{
				MsgType: "order_tip",
				OrderId: req.OrderId,
				Amount:  req.Amount,
			},
		}

		_ = a.nats.Publish("orders.tip", "", push)
	}

	return
}
