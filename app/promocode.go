package app

import (
	"context"
	"errors"
	"fmt"
	"time"

	"billing_service/model"

	null "github.com/guregu/null/v6"
)

const TimeFormat = "2006-01-02 15:04:05"

func (a *App) GetClientPromoCodeInfo(ctx context.Context, clientId int, promoCode string) (resp model.PromocodeInfoResponse, errType string, err error) {
	errType, err = a.repo.GetPromocodeValidation(ctx, clientId, promoCode)
	if err != nil {
		return
	}

	promo, err := a.repo.GetPromocodeInfo(ctx, clientId, promoCode)
	if err != nil {
		return
	}

	resp = model.PromocodeInfoResponse{
		Success:            promo.Success,
		CreateTime:         time.Unix(promo.CreateTime, 0).Format(TimeFormat),
		Usage:              promo.Usage,
		TotalUsage:         int(promo.TotalUsage),
		IsActive:           promo.IsActive,
		Type:               model.PromoCodeTypes[int8(promo.Type)],
		Value:              promo.Value,
		Limit:              promo.Limit,
		ClientId:           int(promo.ClientId.Int64),
		CorpId:             int(promo.CorpId.Int64),
		CanUseTimes:        promo.CanUseTimes,
		UsedTimes:          promo.UsedTimes,
		IsSumForOneOrder:   promo.IsSumForOneOrder,
		EachClientUseTimes: promo.EachClientUseTimes,
		IsForOneClient:     promo.IsForOneClient,
	}

	if promo.ExpireTime > 0 {
		resp.ExpireTime = time.Unix(promo.ExpireTime, 0).Format(TimeFormat)
	}

	// if resp.Type == "discount_on_amount" && resp.IsForOneClient && !resp.IsSumForOneOrder {
	// 	if resp.Value > resp.TotalUsage {
	// 		resp.Value -= resp.TotalUsage
	// 	} else {
	// 		resp.Value = 0
	// 	}
	// }

	// resp.TariffId, err = a.repo.GetPromocodeTariffs(promoCode)
	// if err != nil {
	// 	return
	// }

	return
}

func (a *App) XpanelGetPromocodes(ctx context.Context, code string, clientId int, isActive null.Bool, page, limit int) (resp model.PromocodeResponse, err error) {
	totalCount, err := a.repo.XpanelGetPromocodesCount(ctx, code, clientId)
	if err != nil {
		return
	}

	resp.Data, err = a.repo.XpanelGetPromocodes(ctx, code, clientId, isActive, page, limit)
	if err != nil {
		return
	}

	resp.Count = len(resp.Data)

	resp.TotalCount = totalCount

	return
}

func (a *App) XpanelAddPromocode(ctx context.Context, code string, clientId, validDays null.Int) (err error) {
	promo, err := a.repo.GetPromocodeInfo(ctx, 0, code)
	if err != nil {
		err = fmt.Errorf("get promocode info: %v", err)
		return
	}

	if !promo.IsActive {
		err = errors.New("promocode not active")
		return
	}

	p := model.XpanelPromocode{
		Code:            code,
		ClientId:        clientId,
		ValidDays:       validDays,
		Value:           promo.Value,
		IsForNewClients: promo.IsForNewClients,
	}

	switch promo.Type {
	case 2, 4:
		p.Type = "amount"
	case 3:
		p.Type = "percent"
	default:
		err = fmt.Errorf("unknown promocode type: %d", promo.Type)
		return
	}

	if promo.ExpireTime > 0 {
		if time.Now().Unix() > promo.ExpireTime {
			err = errors.New("promocode expired")
			return
		}
		p.ExpireTime = null.TimeFrom(time.Unix(promo.ExpireTime, 0))
	}
	if promo.CreateTime > 0 {
		p.CreateTime = time.Unix(promo.CreateTime, 0)
	} else {
		p.CreateTime = time.Now()
	}
	if promo.EachClientUseTimes > 0 {
		p.Limit = null.IntFrom(int64(promo.EachClientUseTimes))
	}
	if promo.Limit > 0 {
		p.DiscountLimit = null.IntFrom(int64(promo.Limit))
	}

	err = a.repo.XpanelAddPromocode(ctx, p)

	return
}
