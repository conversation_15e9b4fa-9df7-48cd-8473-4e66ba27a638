package app

import (
	"context"
	"errors"
	"fmt"
	"time"

	"billing_service/model"
	"billing_service/util/fernet"
	"billing_service/util/provider/ubk"

	"github.com/google/uuid"
	null "github.com/guregu/null/v6"
	"github.com/riverqueue/river"
)

func (a *App) payA2CWithUbk(ctx context.Context, req model.A2CPaymentRequest, attempt int) (err error) {
	if attempt > 1 {
		var payment model.Payment
		payment, err = a.repo.GetPaymentStatusByOrderId(ctx, req.OrderId, req.Reason)
		if err != nil {
			return
		}

		if payment.IsFinished() {
			return
		}
		if payment.IsCreated() {
			var resp ubk.Response
			resp, err = a.ubk.CheckStatus(ctx, payment.Invoice)
			if err != nil {
				if err == ubk.ErrReceiptsNotFound {
					err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusCancelled)
					if err != nil {
						return
					}
				} else {
					err = fmt.Errorf("ubk check status: %v", err)
					return
				}
			} else {
				switch resp.Result.State {
				case ubk.PaymentStatusSuccess:
					err = a.repo.CreateBillingPayment(ctx, model.WithdrawPaymentRequest{AccountNumber: "universal_bank", Amount: req.Amount, OrderID: req.OrderId})
					if err != nil {
						return
					}
					err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusFinished)
					if err != nil {
						return
					}
					switch req.Reason {
					case model.PaymentReasonPayOrderForDriver:
						err = a.repo.UpdateOrderDriverPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
						if err != nil {
							return
						}
					case model.PaymentReasonPayPromo:
						_, _, err = a.DriverPromoBalanceToCard(ctx, req.DriverId, req.OrderId, int(req.Amount), fmt.Sprintf("Promo cost to driver card - %d", req.OrderId))
						if err != nil {
							return
						}
					case model.PaymentReasonDriverBonus:
						err = a.publishDriverBonusFiscalization(req)
						if err != nil {
							a.log.Errorf("failed to publish driver bonus fiscalization: %v", err)
						}
					}
					a.repo.SendDriverNotification(req.DriverId, req.OrderId, req.Amount, fmt.Sprintf("driver_order:%d:paid", req.OrderId), "driver_order_paid_to_card")
					return
				case ubk.PaymentStatusInProgress1, ubk.PaymentStatusInProgress2, 1:
					// err = errors.New("ubk pay in process")
					if attempt < 3 {
						err = river.JobSnooze(60 * time.Second) // pay in process
					} else {
						err = errors.New("pay in process")
					}
					return
				default:
					// case 21, 50, 33, 22:
					err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusCancelled)
					if err != nil {
						return
					}
				}
			}
		}
	}

	transactionId := uuid.NewString()

	params := ubk.Params{
		TransactionId: transactionId,
		Amount:        req.Amount,
		CardNumber:    string(fernet.VerifyAndDecrypt([]byte(req.CardNumber), a.fernetKey)),
	}

	resp, err := a.ubk.CreateCheck(ctx, params)
	if err != nil {
		return
	}

	if resp.Result.State == ubk.PaymentStatusCreated {
		payment := model.Payment{
			OrderId:    null.IntFrom(int64(req.OrderId)),
			UserId:     req.DriverId,
			UserType:   "driver",
			CardId:     null.IntFrom(int64(req.CardId)),
			Amount:     req.Amount,
			Invoice:    transactionId,
			Status:     model.PaymentStatusCreated,
			ProviderId: model.ProviderIdUbk,
			Reason:     req.Reason,
		}

		err = a.repo.CreatePayment(ctx, payment)
		if err != nil {
			return
		}
	} else {
		err = fmt.Errorf("ubk create check: %v", resp.Result.State)
		return
	}

	resp, err = a.ubk.PayCheck(ctx, transactionId)
	if err != nil {
		err = fmt.Errorf("ubk pay check: %v", err)
		return
	}

	switch resp.Result.State {
	case ubk.PaymentStatusSuccess:
		err = a.repo.CreateBillingPayment(ctx, model.WithdrawPaymentRequest{AccountNumber: "universal_bank", Amount: req.Amount, OrderID: req.OrderId})
		if err != nil {
			return
		}
		err = a.repo.UpdatePaymentStatus(ctx, transactionId, model.PaymentStatusFinished)
		if err != nil {
			return
		}
		switch req.Reason {
		case model.PaymentReasonPayOrderForDriver:
			err = a.repo.UpdateOrderDriverPaymentStatus(ctx, req.OrderId, model.OrderPaymentStatusSuccess)
			if err != nil {
				return
			}
		case model.PaymentReasonPayPromo:
			_, _, err = a.DriverPromoBalanceToCard(ctx, req.DriverId, req.OrderId, int(req.Amount), fmt.Sprintf("Promo cost to driver card - %d", req.OrderId))
			if err != nil {
				return
			}
		case model.PaymentReasonDriverBonus:
			err = a.publishDriverBonusFiscalization(req)
			if err != nil {
				a.log.Errorf("failed to publish driver bonus fiscalization: %v", err)
			}
		}
		a.repo.SendDriverNotification(req.DriverId, req.OrderId, req.Amount, fmt.Sprintf("driver_order:%d:paid", req.OrderId), "driver_order_paid_to_card")

	case ubk.PaymentStatusInProgress1, ubk.PaymentStatusInProgress2, 1:
		if attempt < 3 {
			err = river.JobSnooze(60 * time.Second) // pay in process
		} else {
			err = errors.New("pay in process")
		}
		return
	default:
		err = fmt.Errorf("ubk pay unsuccessfull: %v", resp.Result)
		return
	}

	return
}

func (a *App) publishDriverBonusFiscalization(req model.A2CPaymentRequest) error {
	receiptId := fmt.Sprintf("challenge_%d", req.OrderId)
	receipt := model.FiscalizationBonusReceipt{
		ReceiptId: receiptId,
		DriverId:  req.DriverId,
		Price:     req.Amount,
		Datetime:  time.Now(),
	}

	msgId := fmt.Sprintf("driver_bonus_fiscalization:%d:%s", req.DriverId, receiptId)
	err := a.nats.Publish("orders.fiscalization.bonus", msgId, receipt)
	if err != nil {
		return fmt.Errorf("failed to publish to orders.fiscalization.bonus: %v", err)
	}

	return nil
}
