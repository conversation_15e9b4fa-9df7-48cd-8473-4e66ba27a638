package app

import (
	"context"
	"fmt"
)

func (a *App) CorpDeposit(ctx context.Context, corpId, amount, amountType int, comment string) (err error) {
	reqId := fmt.Sprintf("corp:%d:amount:%d:type:%d:deposit:%s", corpId, amount, amountType, comment)

	exists, err := a.setRequestID(ctx, reqId)
	if err != nil || exists {
		return
	}

	err = a.repo.CorpDeposit(ctx, corpId, amount, amountType, comment)
	if err != nil {
		err = fmt.Errorf("billing click error: %v", err)
		_ = a.redis.Del(ctx, reqId)
	}

	return
}
