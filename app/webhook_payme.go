package app

import (
	"context"
	"fmt"
	"time"

	"billing_service/model"
	"billing_service/util"
	"billing_service/util/provider/payme"
)

func (a *App) PaymeDriverWebhook(ctx context.Context, req payme.WebhookRequest) (resp payme.WebhookResponse, err error) {
	defer func() {
		if err != nil {
			resp.SetAppError(err.Error())
		}
	}()

	switch req.Method {
	case "CheckPerformTransaction":
		// Метод проверяет может ли система произвести транзацкию. Т.Е. в этом методе идет валидация данных.
		// Проверяется валидность водителя - есть ли такой водитель, валидность суммы

		if req.Params.Account == nil {
			resp.SetRPCRequestError()
			return
		}

		if req.Params.Amount.Int64 <= 0 {
			resp.SetAmountError()
			return
		}

		var exists bool
		if req.Params.Account.UserId.Int64 > 0 {
			exists, err = a.repo.GetDriverExists(ctx, int(req.Params.Account.UserId.Int64))
			if err != nil {
				return
			}
		}

		if !exists {
			resp.SetAccountDataError("Водитель с таким ID не найден")
			return
		}

		resp.Result = &payme.WebhookResult{Allow: true}

	case "CreateTransaction":
		// Метод для создании транзакции. Создается запись в таблице driver_paycom_transactions.
		// Если по такой ID транзакции уже есть запись в базе, то возвращается статус этой транзакции.

		if req.Params.ReceiptId.String == "" ||
			req.Params.Account == nil ||
			req.Params.Account.UserId.Int64 == 0 ||
			req.Params.Amount.Int64 == 0 {
			resp.SetRPCRequestError()
			return
		}

		var payment model.Payment
		payment, err = a.repo.GetPaymentStatusByInvoice(ctx, req.Params.ReceiptId.String)
		if err != nil {
			return
		}

		if payment.Invoice != "" {
			if payment.Status == model.PaymentStatusCreated {
				resp.Result = &payme.WebhookResult{
					Transaction: payment.Invoice,
					State:       model.PaymentStatusCreated,
					CreateTime:  time.Now().UnixMilli(),
				}
			} else {
				resp.SetCannotBePerformedError()
			}
			return
		}

		var reason int

		if req.Params.Account.PaymentMethod.String == "driver_refill" {
			reason = model.PaymentReasonAutorefillBalance
		} else {
			reason = model.PaymentReasonRefillBalance
		}

		payment = model.Payment{
			UserId:     int(req.Params.Account.UserId.Int64),
			UserType:   "driver",
			Amount:     util.TiyinToSoum(int(req.Params.Amount.Int64)), // TODO tiyin to soum
			OrderId:    req.Params.Account.OrderId,
			Status:     model.PaymentStatusCreated,
			Reason:     reason,
			Invoice:    req.Params.ReceiptId.String,
			ProviderId: model.ProviderIdPayme,
		}
		err = a.repo.CreatePayment(ctx, payment)
		if err != nil {
			return
		}

		resp.Result = &payme.WebhookResult{
			Transaction: req.Params.ReceiptId.String,
			State:       model.PaymentStatusCreated,
			CreateTime:  time.Now().UnixMilli(),
		}

	case "PerformTransaction":
		// Метод для проведения транзацкии. Это последующий процесс после проверки и создании транзакции (CheckPerformTransaction, CreateTransactions).
		// Определяется запись в таблице по идентификатору транзакции. Если такой транзакции не существует возвращается ошибка.
		// Если статус транзакции ОТМЕНЕН - то надо вернуть ошибку.
		// Если статус транзакции СОЗДАН - то сохраняем остальные детали и  меняем статус в базе.

		if req.Params.ReceiptId.String == "" {
			resp.SetRPCRequestError()
			return
		}

		var payment model.PaymentDetails
		payment, err = a.repo.GetPaymentDetailsByInvoice(ctx, req.Params.ReceiptId.String)
		if err != nil {
			return
		}

		if payment.Invoice == "" {
			resp.SetTransactionNotFoundError()
			return
		}

		if payment.Status != model.PaymentStatusCreated {
			if payment.Status == model.PaymentStatusFinished {
				resp.Result = &payme.WebhookResult{
					Transaction: payment.Invoice,
					State:       model.PaymentStatusFinished,
					PerformTime: time.Now().UnixMilli(),
				}
			} else {
				resp.SetCannotBePerformedError()
			}
			return
		}

		msgId := fmt.Sprintf("driver:%d:balance_refill:%s", payment.UserId, payment.Invoice)
		err = a.RefillDriverBalance(ctx, payment.UserId, payment.Amount, 0, 0, "Пополнение баланса", msgId)
		if err != nil {
			err = fmt.Errorf("fill driver %d balance: %v", payment.UserId, err)
			return
		}

		if payment.Reason == model.PaymentReasonAutorefillBalance {
			a.repo.SendDriverNotification(payment.UserId, int(payment.OrderId.Int64), payment.Amount, msgId, "driver_balance_auto_refilled")
		} else {
			a.repo.SendDriverNotification(payment.UserId, 0, payment.Amount, msgId, "driver_balance_refilled")
		}

		err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusFinished)
		if err != nil {
			return
		}

		resp.Result = &payme.WebhookResult{
			Transaction: payment.Invoice,
			State:       model.PaymentStatusFinished,
			PerformTime: time.Now().UnixMilli(),
		}

	case "CheckTransaction":
		// Проверяет имеется ли в таблице соответсвущая запись по идентификаторы транзакции. Если да, надо вернуть детали.

		if req.Params.ReceiptId.String == "" {
			resp.SetRPCRequestError()
			return
		}

		var payment model.PaymentDetails
		payment, err = a.repo.GetPaymentDetailsByInvoice(ctx, req.Params.ReceiptId.String)
		if err != nil {
			return
		}

		if payment.Invoice == "" {
			resp.SetTransactionNotFoundError()
			return
		}

		now := time.Now().UnixMilli()

		resp.Result = &payme.WebhookResult{
			Transaction: payment.Invoice,
			State:       payment.Status,
			CreateTime:  now,
			Reason:      payment.Reason,
		}

		switch payment.Status {
		case model.PaymentStatusFinished:
			resp.Result.PerformTime = now
		case model.PaymentStatusCancelled, model.PaymentStatusCancelledAfterFinish:
			resp.Result.CancelTime = now
		}

	case "CancelTransaction":
		// Отмена транзацкии. Также стоит указать причину отмены и обновить данные в таблице.

		if req.Params.ReceiptId.String == "" {
			resp.SetRPCRequestError()
			return
		}

		var payment model.PaymentDetails
		payment, err = a.repo.GetPaymentDetailsByInvoice(ctx, req.Params.ReceiptId.String)
		if err != nil {
			return
		}

		if payment.Invoice == "" {
			resp.SetTransactionNotFoundError()
			return
		}

		if payment.Status == model.PaymentStatusCancelled || payment.Status == model.PaymentStatusCancelledAfterFinish {
			resp.Result = &payme.WebhookResult{
				Transaction: payment.Invoice,
				State:       payment.Status,
				CancelTime:  time.Now().UnixMilli(),
			}
			return
		}

		status := model.PaymentStatusCancelledAfterFinish

		if payment.Status != model.PaymentStatusFinished {
			status = model.PaymentStatusCancelled
		}

		err = a.repo.UpdatePaymentStatus(ctx, req.Params.ReceiptId.String, status)
		if err != nil {
			return
		}

		if payment.Status == model.PaymentStatusFinished {
			var comment string

			if payment.OrderId.Int64 > 0 {
				comment = fmt.Sprintf("Возврат денег по заказу %d", payment.OrderId.Int64)
			} else {
				comment = "Отмена пополнения баланса"
			}

			reqId := fmt.Sprintf("driver:%d:payme:%scancel", payment.UserId, payment.Invoice)

			err = a.RefineDriverBalance(ctx, payment.UserId, payment.Amount, 0, comment, reqId)
		}

		resp.Result = &payme.WebhookResult{
			Transaction: payment.Invoice,
			State:       status,
			CancelTime:  time.Now().UnixMilli(),
		}

	default:
		resp.SetMethodNotFoundError()
	}

	return
}

func (a *App) PaymeClientWebhook(ctx context.Context, req payme.WebhookRequest) (resp payme.WebhookResponse, err error) {
	defer func() {
		if err != nil {
			resp.SetAppError(err.Error())
		}
	}()

	//  TODO driver refill work to client account

	if req.Params.Account != nil && req.Params.Account.PaymentMethod.String == "driver_refill" {
		return a.PaymeDriverWebhook(ctx, req)
	} else if req.Params.ReceiptId.String != "" {
		var payment model.PaymentDetails
		payment, err = a.repo.GetPaymentDetailsByInvoice(ctx, req.Params.ReceiptId.String)
		if err != nil {
			return
		}
		if payment.Invoice != "" && payment.Reason == model.PaymentReasonAutorefillBalance {
			return a.PaymeDriverWebhook(ctx, req)
		}
	}

	switch req.Method {
	case "CheckPerformTransaction":
		// Метод проверяет может ли система произвести транзацкию. Т.Е. в этом методе идет валидация данных.
		// Проверяется валидность карты клиента - есть ли такая карта, валидность суммы

		if req.Params.Account == nil {
			resp.SetRPCRequestError()
			return
		}

		if req.Params.Amount.Int64 <= 0 {
			resp.SetAmountError()
			return
		}

		var card model.Card
		if req.Params.Account.CardId.Int64 > 0 {
			card, err = a.repo.GetCard(ctx, int(req.Params.Account.CardId.Int64))
			if err != nil {
				return
			}
		}

		if card.Id == 0 {
			resp.SetAccountDataError("Карта клиента не найдена")
			return
		}

		resp.Result = &payme.WebhookResult{Allow: true}

	case "CreateTransaction":
		// Метод для создании транзакции. Создается запись в таблице client_paycom_transactions.
		// Если по такой ID транзакции уже есть запись в базе, то возвращается статус этой транзакции.

		if req.Params.ReceiptId.String == "" ||
			req.Params.Account == nil ||
			req.Params.Account.UserId.Int64 == 0 ||
			req.Params.Account.CardId.Int64 == 0 ||
			req.Params.Amount.Int64 == 0 {
			resp.SetRPCRequestError()
			return
		}

		var payment model.Payment
		payment, err = a.repo.GetPaymentStatusByInvoice(ctx, req.Params.ReceiptId.String)
		if err != nil {
			return
		}

		if payment.Invoice != "" {
			if payment.Status == model.PaymentStatusCreated {
				resp.Result = &payme.WebhookResult{
					Transaction: payment.Invoice,
					State:       model.PaymentStatusCreated,
					CreateTime:  time.Now().UnixMilli(),
				}
			} else {
				resp.SetCannotBePerformedError()
			}
			return
		}

		var reason int

		switch req.Params.Account.PaymentMethod.String {
		case "client_debt":
			reason = model.PaymentReasonPayDebt
		case "client_order":
			reason = model.PaymentReasonPayOrder
		case "client_tips":
			reason = model.PaymentReasonPayTips
		default:
			reason = model.PaymentReasonPayOrder
		}

		payment = model.Payment{
			UserId:     int(req.Params.Account.UserId.Int64),
			UserType:   "client",
			Amount:     util.TiyinToSoum(int(req.Params.Amount.Int64)), // TODO tiyin to soum
			OrderId:    req.Params.Account.OrderId,
			CardId:     req.Params.Account.CardId,
			Status:     model.PaymentStatusCreated,
			Reason:     reason,
			Invoice:    req.Params.ReceiptId.String,
			ProviderId: model.ProviderIdPayme,
		}
		err = a.repo.CreatePayment(ctx, payment)
		if err != nil {
			return
		}

		resp.Result = &payme.WebhookResult{
			Transaction: req.Params.ReceiptId.String,
			State:       model.PaymentStatusCreated,
			CreateTime:  time.Now().UnixMilli(),
		}

	case "PerformTransaction":
		// Метод для проведения транзацкии. Это последующий процесс после проверки и создании транзакции (CheckPerformTransaction, CreateTransactions).
		// Определяется запись в таблице по идентификатору транзакции. Если такой транзакции не существует возвращается ошибка.
		// Если статус транзакции ОТМЕНЕН - то надо вернуть ошибку.
		// Если статус транзакции СОЗДАН - то сохраняем остальные детали и  меняем статус в базе.

		if req.Params.ReceiptId.String == "" {
			resp.SetRPCRequestError()
			return
		}

		var payment model.Payment
		payment, err = a.repo.GetPaymentStatusByInvoice(ctx, req.Params.ReceiptId.String)
		if err != nil {
			return
		}

		if payment.Invoice == "" {
			resp.SetTransactionNotFoundError()
			return
		}

		if payment.Status != model.PaymentStatusCreated {
			if payment.Status == model.PaymentStatusFinished {
				resp.Result = &payme.WebhookResult{
					Transaction: payment.Invoice,
					State:       model.PaymentStatusFinished,
					PerformTime: time.Now().UnixMilli(), // TODO
				}
			} else {
				resp.SetCannotBePerformedError()
			}
			return
		}

		err = a.repo.UpdatePaymentStatus(ctx, payment.Invoice, model.PaymentStatusFinished)
		if err != nil {
			return
		}

		resp.Result = &payme.WebhookResult{
			Transaction: payment.Invoice,
			State:       model.PaymentStatusFinished,
			PerformTime: time.Now().UnixMilli(),
		}

	case "CheckTransaction":
		// Проверяет имеется ли в таблице соответсвущая запись по идентификаторы транзакции. Если да, надо вернуть детали.

		if req.Params.ReceiptId.String == "" {
			resp.SetRPCRequestError()
			return
		}

		var payment model.PaymentDetails
		payment, err = a.repo.GetPaymentDetailsByInvoice(ctx, req.Params.ReceiptId.String)
		if err != nil {
			return
		}

		if payment.Invoice == "" {
			resp.SetTransactionNotFoundError()
			return
		}

		now := time.Now().UnixMilli()

		resp.Result = &payme.WebhookResult{
			Transaction: payment.Invoice,
			State:       payment.Status,
			CreateTime:  now,
			Reason:      payment.Reason,
		}

		if payment.Status == model.PaymentStatusFinished {
			resp.Result.PerformTime = now
		} else if payment.Status == model.PaymentStatusCancelled || payment.Status == model.PaymentStatusCancelledAfterFinish {
			resp.Result.CancelTime = now
		}

	case "CancelTransaction":
		// Отмена транзацкии. Также стоит указать причину отмены и обновить данные в таблице.

		if req.Params.ReceiptId.String == "" {
			resp.SetRPCRequestError()
			return
		}

		var payment model.PaymentDetails
		payment, err = a.repo.GetPaymentDetailsByInvoice(ctx, req.Params.ReceiptId.String)
		if err != nil {
			return
		}

		if payment.Invoice == "" {
			resp.SetTransactionNotFoundError()
			return
		}

		if payment.Status == model.PaymentStatusCancelled || payment.Status == model.PaymentStatusCancelledAfterFinish {
			resp.Result = &payme.WebhookResult{
				Transaction: payment.Invoice,
				State:       payment.Status,
				CancelTime:  time.Now().UnixMilli(),
			}
			return
		}

		status := model.PaymentStatusCancelledAfterFinish

		if payment.Status != model.PaymentStatusFinished {
			status = model.PaymentStatusCancelled
		}

		err = a.repo.UpdatePaymentStatus(ctx, req.Params.ReceiptId.String, status)
		if err != nil {
			return
		}

		resp.Result = &payme.WebhookResult{
			Transaction: payment.Invoice,
			State:       status,
			CancelTime:  time.Now().UnixMilli(),
		}

	default:
		resp.SetMethodNotFoundError()
	}

	return
}
