package app

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"billing_service/model"
	"billing_service/util/provider/atmos"

	"github.com/google/uuid"
	null "github.com/guregu/null/v6"
	"github.com/riverqueue/river"
)

func (a *App) PayTipsAtmosTask(ctx context.Context, req model.PayTipsRequest, attempt int) (err error) {
	var (
		isPaid  bool
		comment = "Чаевые за заказ " + strconv.Itoa(req.OrderId)
	)

	// Check if we already have a payment in progress
	if attempt > 1 {
		var payment model.Payment
		payment, err = a.repo.GetPaymentStatusByOrderId(ctx, req.OrderId, model.PaymentReasonPayTips)
		if err != nil {
			return
		}

		if payment.IsFinished() {
			isPaid = true
		} else if payment.IsCreated() && payment.ProviderId == model.ProviderIdAtmos {
			// Check transaction status
			transactionID := payment.Invoice
			transactionResp, err := a.atmos.GetTransaction(ctx, transactionID)
			if err != nil {
				return fmt.Errorf("get transaction status: %v", err)
			}

			switch {
			case transactionResp.Payload.IsPending():
				if attempt < 3 {
					return river.JobSnooze(60 * time.Second) // payment in process
				} else {
					return errors.New("payment in process")
				}
			case transactionResp.Payload.IsSuccess():
				isPaid = true
				err = a.repo.UpdatePaymentStatus(ctx, transactionID, model.PaymentStatusFinished)
				if err != nil {
					return err
				}

			case transactionResp.Payload.IsFailed():
				// Cancel the payment
				err = a.repo.UpdatePaymentStatus(ctx, transactionID, model.PaymentStatusCancelled)
				if err != nil {
					return err
				}

				card, err := a.repo.GetCardById(ctx, int(req.CardId), req.ClientId, "client")
				if err == nil && card.AtmosHash.String != "" {
					if err := a.repo.IncrementCardPaymentAttempts(ctx, card.AtmosHash.String); err != nil {
						a.log.Errorf("Failed to increment card payment attempts: %v", err)
					}
				}
			}
		}
	}

	// Get driver ID if not provided
	if req.DriverId == 0 {
		req.DriverId, err = a.repo.GetOrderDriverId(ctx, req.OrderId)
		if err != nil {
			return
		}
		if req.DriverId == 0 {
			return errors.New("driver not found for order")
		}
	}

	if !isPaid {
		card, err := a.repo.GetCardById(ctx, int(req.CardId), req.ClientId, "client")
		if err != nil {
			return fmt.Errorf("get client card: %v", err)
		}

		if card.AtmosToken.String == "" {
			return errors.New("client card atmos token not found")
		}

		if card.AtmosHash.String != "" {
			attemptCount, err := a.repo.GetCardPaymentAttempts(ctx, card.AtmosHash.String)
			if err != nil {
				a.log.Errorf("Failed to get card payment attempts: %v", err)
			} else if attemptCount >= MaxAtmosAttempts {
				a.log.Infof("Card with hash %s has exceeded maximum payment attempts (%d). Skipping tips payment.",
					card.AtmosHash.String, attemptCount)
				return errors.New("card has exceeded maximum payment attempts")
			}
		}

		// Get client card details
		cardDetails, err := a.atmos.GetCardDetails(ctx, card.AtmosToken.String)
		if err != nil {
			return fmt.Errorf("get card details: %v", err)
		}

		// Create a unique invoice ID for this transaction
		orderID := strconv.Itoa(req.OrderId)
		invoiceID := fmt.Sprintf("tips_%s_%s", orderID, uuid.NewString())

		// Create hold transaction
		holdReq := atmos.UklonHoldRequest{
			ExtID:        uuid.NewString(),
			Amount:       atmos.SoumToTiyin(req.Amount), // Convert UZS to tiyin (multiply by 100)
			StoreID:      strconv.Itoa(a.cfg.Atmos.StoreID),
			Account:      orderID,
			InvoiceID:    invoiceID,
			CardID:       cardDetails.Payload.Card.ID,
			ClientIPAddr: "127.0.0.1",
		}

		holdResp, err := a.atmos.UklonHold(ctx, holdReq)
		if err != nil {
			return fmt.Errorf("atmos hold request: %v", err)
		}

		if !holdResp.Payload.IsSuccess() {
			if card.AtmosHash.String != "" {
				if err := a.repo.IncrementCardPaymentAttempts(ctx, card.AtmosHash.String); err != nil {
					a.log.Errorf("Failed to increment card payment attempts: %v", err)
				}
			}
			return fmt.Errorf("atmos hold transaction not successful: %s (status: %s)",
				holdResp.Payload.ResultCode,
				holdResp.Payload.GetTransactionStatus().String())
		}

		transactionID := fmt.Sprintf("%d", holdResp.Payload.ID)

		// Create payment record
		payment := model.Payment{
			OrderId:    null.IntFrom(int64(req.OrderId)),
			UserId:     req.ClientId,
			UserType:   "client",
			CardId:     null.IntFrom(int64(req.CardId)),
			Amount:     req.Amount,
			Invoice:    transactionID,
			Status:     model.PaymentStatusCreated,
			ProviderId: model.ProviderIdAtmos,
			Reason:     model.PaymentReasonPayTips,
		}

		err = a.repo.CreatePayment(ctx, payment)
		if err != nil {
			return fmt.Errorf("create payment: %v", err)
		}

		// Apply the transaction
		applyResp, err := a.atmos.UklonApply(ctx, transactionID)
		if err != nil {
			return fmt.Errorf("atmos apply request: %v", err)
		}

		switch {
		case applyResp.Payload.IsPending():
			return river.JobSnooze(60 * time.Second)

		case applyResp.Payload.IsFailed():
			err = a.repo.UpdatePaymentStatus(ctx, transactionID, model.PaymentStatusCancelled)
			if err != nil {
				return err
			}

			if card.AtmosHash.String != "" {
				if err := a.repo.IncrementCardPaymentAttempts(ctx, card.AtmosHash.String); err != nil {
					a.log.Errorf("Failed to increment card payment attempts: %v", err)
				}
			}
			return fmt.Errorf("atmos apply transaction failed: %s (status: %s)",
				applyResp.Payload.ResultCode,
				applyResp.Payload.GetTransactionStatus().String())

		case !applyResp.Payload.IsSuccess():
			err = a.repo.UpdatePaymentStatus(ctx, transactionID, model.PaymentStatusCancelled)
			if err != nil {
				return err
			}

			if card.AtmosHash.String != "" {
				if err := a.repo.IncrementCardPaymentAttempts(ctx, card.AtmosHash.String); err != nil {
					a.log.Errorf("Failed to increment card payment attempts: %v", err)
				}
			}
			return fmt.Errorf("atmos apply transaction not successful: %s (status: %s)",
				applyResp.Payload.ResultCode,
				applyResp.Payload.GetTransactionStatus().String())
		}

		// Update payment status to finished
		err = a.repo.UpdatePaymentStatus(ctx, transactionID, model.PaymentStatusFinished)
		if err != nil {
			return fmt.Errorf("update payment status: %v", err)
		}

		isPaid = true
	}

	if isPaid {
		r := model.A2CPaymentRequest{
			OrderId:  req.OrderId,
			DriverId: req.DriverId,
			Amount:   req.Amount,
			Reason:   model.PaymentReasonPayTips,
			Comment:  comment,
		}

		_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: r}, nil)
		if err != nil {
			return fmt.Errorf("failed to create A2C payment for driver: %v", err)
		}
	}

	if attempt <= 5 {
		push := model.DriverSocketNotification{
			DriverId: req.DriverId,
			Body: model.DriverSocketNotificationBody{
				MsgType: "order_tip",
				OrderId: req.OrderId,
				Amount:  req.Amount,
			},
		}

		_ = a.nats.Publish("orders.tip", "", push)
	}

	return
}
