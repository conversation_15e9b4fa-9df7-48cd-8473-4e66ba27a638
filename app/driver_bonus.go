package app

import (
	"context"
	"fmt"

	"billing_service/model"
)

func (a *App) ProcessDriverBonus(ctx context.Context, req model.DriverBonusEvent) (err error) {
	comment := fmt.Sprintf("Driver bonus payment for challenge %d", req.ChallengeId)

	a2cRequest := model.A2CPaymentRequest{
		OrderId:  req.ChallengeId,
		DriverId: req.DriverId,
		Amount:   req.BonusAmount,
		Reason:   model.PaymentReasonDriverBonus,
		Comment:  comment,
	}

	_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: a2cRequest}, nil)

	return
}
