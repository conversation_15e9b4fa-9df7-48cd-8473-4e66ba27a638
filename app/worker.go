package app

import (
	"context"
	"time"

	"billing_service/model"

	"github.com/riverqueue/river"
)

type RiverMetadata struct {
	Snoozes int `json:"snoozes"`
}

type PayOrderPaymeArgs struct {
	model.PayOrderRequest
}

func (PayOrderPaymeArgs) Kind() string { return "pay_order_payme" }

func (PayOrderPaymeArgs) InsertOpts() river.InsertOpts {
	return river.InsertOpts{
		MaxAttempts: 50,
		UniqueOpts: river.UniqueOpts{
			ByArgs:   true,
			ByPeriod: 24 * time.Hour,
		},
	}
}

type PayOrderPaymeWorker struct {
	app *App
	river.WorkerDefaults[PayOrderPaymeArgs]
}

func (w *PayOrderPaymeWorker) Work(ctx context.Context, job *river.Job[PayOrderPaymeArgs]) error {
	var m RiverMetadata
	_ = json.Unmarshal(job.Metadata, &m)
	job.Attempt += m.Snoozes
	return w.app.PayOrderPaymeTask(ctx, job.Args.PayOrderRequest, job.Attempt)
}

// -----------------------------------------------------------------------------------------------------

type PayOrderAtmosArgs struct {
	model.PayOrderRequest
}

func (PayOrderAtmosArgs) Kind() string { return "pay_order_atmos" }

func (PayOrderAtmosArgs) InsertOpts() river.InsertOpts {
	return river.InsertOpts{
		MaxAttempts: 50,
		UniqueOpts: river.UniqueOpts{
			ByArgs:   true,
			ByPeriod: 24 * time.Hour,
		},
	}
}

type PayOrderAtmosWorker struct {
	app *App
	river.WorkerDefaults[PayOrderAtmosArgs]
}

func (w *PayOrderAtmosWorker) Work(ctx context.Context, job *river.Job[PayOrderAtmosArgs]) error {
	var m RiverMetadata
	_ = json.Unmarshal(job.Metadata, &m)
	job.Attempt += m.Snoozes
	return w.app.PayOrderAtmosTask(ctx, job.Args.PayOrderRequest, job.Attempt)
}

// -----------------------------------------------------------------------------------------------------

type PayA2CArgs struct {
	model.A2CPaymentRequest
}

func (PayA2CArgs) Kind() string { return "pay_account_to_card" }

func (PayA2CArgs) InsertOpts() river.InsertOpts {
	return river.InsertOpts{
		MaxAttempts: 50,
		UniqueOpts: river.UniqueOpts{
			ByArgs:   true,
			ByPeriod: 24 * time.Hour,
		},
	}
}

type PayA2CWorker struct {
	app *App
	river.WorkerDefaults[PayA2CArgs]
}

func (w *PayA2CWorker) Work(ctx context.Context, job *river.Job[PayA2CArgs]) error {
	var m RiverMetadata
	_ = json.Unmarshal(job.Metadata, &m)
	job.Attempt += m.Snoozes
	return w.app.PayA2CTask(ctx, job.Args.A2CPaymentRequest, job.Attempt)
}

// -----------------------------------------------------------------------------------------------------

type PayTipsArgs struct {
	model.PayTipsRequest
}

func (PayTipsArgs) Kind() string { return "pay_tips" }

func (PayTipsArgs) InsertOpts() river.InsertOpts {
	return river.InsertOpts{
		MaxAttempts: 10,
		UniqueOpts: river.UniqueOpts{
			ByArgs:   true,
			ByPeriod: 6 * time.Hour,
		},
	}
}

type PayTipsWorker struct {
	app *App
	river.WorkerDefaults[PayTipsArgs]
}

func (w *PayTipsWorker) Work(ctx context.Context, job *river.Job[PayTipsArgs]) error {
	var m RiverMetadata
	_ = json.Unmarshal(job.Metadata, &m)
	job.Attempt += m.Snoozes
	return w.app.PayTipsTask(ctx, job.Args.PayTipsRequest, job.Attempt)
}

// -----------------------------------------------------------------------------------------------------

type PayTipsAtmosArgs struct {
	model.PayTipsRequest
}

func (PayTipsAtmosArgs) Kind() string { return "pay_tips_atmos" }

func (PayTipsAtmosArgs) InsertOpts() river.InsertOpts {
	return river.InsertOpts{
		MaxAttempts: 10,
		UniqueOpts: river.UniqueOpts{
			ByArgs:   true,
			ByPeriod: 6 * time.Hour,
		},
	}
}

type PayTipsAtmosWorker struct {
	app *App
	river.WorkerDefaults[PayTipsAtmosArgs]
}

func (w *PayTipsAtmosWorker) Work(ctx context.Context, job *river.Job[PayTipsAtmosArgs]) error {
	var m RiverMetadata
	_ = json.Unmarshal(job.Metadata, &m)
	job.Attempt += m.Snoozes
	return w.app.PayTipsAtmosTask(ctx, job.Args.PayTipsRequest, job.Attempt)
}

// -----------------------------------------------------------------------------------------------------

type PayPromoArgs struct {
	model.PayPromoRequest
}

func (PayPromoArgs) Kind() string { return "pay_promo" }

func (PayPromoArgs) InsertOpts() river.InsertOpts {
	return river.InsertOpts{
		MaxAttempts: 50,
		UniqueOpts: river.UniqueOpts{
			ByArgs:   true,
			ByPeriod: 24 * time.Hour,
		},
	}
}

type PayPromoWorker struct {
	app *App
	river.WorkerDefaults[PayPromoArgs]
}

func (w *PayPromoWorker) Work(ctx context.Context, job *river.Job[PayPromoArgs]) error {
	var m RiverMetadata
	_ = json.Unmarshal(job.Metadata, &m)
	job.Attempt += m.Snoozes
	return w.app.PayPromoTask(ctx, job.Args.PayPromoRequest, job.Attempt)
}

// -----------------------------------------------------------------------------------------------------

type ResetCardPaymentAttemptsArgs struct{}

func (ResetCardPaymentAttemptsArgs) Kind() string { return "reset_card_payment_attempts" }

func (ResetCardPaymentAttemptsArgs) InsertOpts() river.InsertOpts {
	return river.InsertOpts{
		MaxAttempts: 3,
	}
}

type ResetCardPaymentAttemptsWorker struct {
	app *App
	river.WorkerDefaults[ResetCardPaymentAttemptsArgs]
}

func (w *ResetCardPaymentAttemptsWorker) Work(ctx context.Context, job *river.Job[ResetCardPaymentAttemptsArgs]) error {
	return w.app.ResetAllCardPaymentAttemptsTask(ctx)
}

// -----------------------------------------------------------------------------------------------------

type RefillDriverBalanceArgs struct {
	model.RefillDriverBalanceRequest
}

func (RefillDriverBalanceArgs) Kind() string { return "refill_driver_balance" }

func (RefillDriverBalanceArgs) InsertOpts() river.InsertOpts {
	return river.InsertOpts{
		MaxAttempts: 20,
		UniqueOpts: river.UniqueOpts{
			ByArgs:   true,
			ByPeriod: 24 * time.Hour,
		},
	}
}

type RefillDriverBalanceWorker struct {
	app *App
	river.WorkerDefaults[RefillDriverBalanceArgs]
}

func (w *RefillDriverBalanceWorker) Work(ctx context.Context, job *river.Job[RefillDriverBalanceArgs]) error {
	var m RiverMetadata
	_ = json.Unmarshal(job.Metadata, &m)
	job.Attempt += m.Snoozes
	return w.app.RefillDriverBalanceTask(ctx, job.Args.RefillDriverBalanceRequest, job.Attempt)
}

// -----------------------------------------------------------------------------------------------------

type GetAndProcessA2AArgs struct{}

func (GetAndProcessA2AArgs) Kind() string { return "get_and_process_a2a" }

func (GetAndProcessA2AArgs) InsertOpts() river.InsertOpts {
	return river.InsertOpts{
		MaxAttempts: 5,
	}
}

type GetAndProcessA2AWorker struct {
	app *App
	river.WorkerDefaults[GetAndProcessA2AArgs]
}

func (w *GetAndProcessA2AWorker) Work(ctx context.Context, job *river.Job[GetAndProcessA2AArgs]) error {
	return w.app.PrepareAndPassA2A(ctx)
}

// -----------------------------------------------------------------------------------------------------

type TransferA2AArgs struct {
	TransferID int
}

func (TransferA2AArgs) Kind() string { return "transfer_a2a" }

func (TransferA2AArgs) InsertOpts() river.InsertOpts {
	return river.InsertOpts{
		MaxAttempts: 25,
	}
}

type TransferA2AWorker struct {
	app *App
	river.WorkerDefaults[TransferA2AArgs]
}

func (w *TransferA2AWorker) Work(ctx context.Context, job *river.Job[TransferA2AArgs]) error {
	err := w.app.TransferA2A(ctx, job.Args.TransferID, job.Attempt)
	return err
}

// -----------------------------------------------------------------------------------------------------
