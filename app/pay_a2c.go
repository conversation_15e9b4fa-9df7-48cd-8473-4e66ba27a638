package app

import (
	"context"
	"errors"
	"fmt"
	"time"

	"billing_service/model"
	"billing_service/util/provider/payme"

	"github.com/riverqueue/river"
)

func (a *App) PayA2CTask(ctx context.Context, req model.A2CPaymentRequest, attempt int) (err error) {
	if attempt > 40 {
		var payment model.Payment
		payment, err = a.repo.GetPaymentStatusByOrderId(ctx, req.OrderId, req.Reason)
		if err != nil {
			return
		}

		if payment.IsFinished() {
			return
		}

		err = a.RefillDriverBalance(ctx, req.DriverId, req.Amount, 0, 0, req.Comment, req.Comment)
		return
	}

	card, err := a.repo.GetCardByUserId(ctx, req.DriverId, "driver")
	if err != nil {
		return
	}

	if card.Id == 0 {
		if attempt < 5 {
			a.repo.SendDriverNotification(req.DriverId, req.OrderId, req.Amount, fmt.Sprintf("driver_order:%d:paid_in_queue:attempt:%d", req.OrderId, attempt), "driver_order_paid_card_error")
			err = river.JobSnooze(15 * time.Minute) // pay in process
			return
		}
		err = a.RefillDriverBalance(ctx, req.DriverId, req.Amount, 0, 0, req.Comment, req.Comment)
		return
	}

	_, err = a.payme.CardCheckLong(ctx, card.PaymeToken.String)
	if err != nil {
		switch err {
		case payme.ErrCardNotFound,
			payme.ErrInvalidTokenFormat,
			payme.ErrCardExpired:
			if attempt < 5 {
				a.repo.SendDriverNotification(req.DriverId, req.OrderId, req.Amount, fmt.Sprintf("driver_order:%d:paid_in_queue:attempt:%d", req.OrderId, attempt), "driver_order_paid_card_error")
				err = river.JobSnooze(15 * time.Minute) // pay in process
				return
			}
			err = a.RefillDriverBalance(ctx, req.DriverId, req.Amount, 0, 0, req.Comment, req.Comment)
			return
		default:
			err = fmt.Errorf("driver payme card check: %v", err)
			return
		}
	}

	if attempt == 4 {
		a.repo.SendDriverNotification(req.DriverId, req.OrderId, req.Amount, fmt.Sprintf("driver_order:%d:paid_in_queue", req.OrderId), "driver_order_paid_in_queue")
	}

	req.CardId = card.Id
	req.CardNumber = card.FullNumber.String

	switch {
	case a.repo.GetSysParam("is_ubk_enabled", "").Bool():
		if req.CardNumber == "" {
			err = errors.New("driver full card number not found for ubk")
			return
		}

		err = a.payA2CWithUbk(ctx, req, attempt)

	default:
		err = errors.New("all a2c providers disabled")
	}

	return
}
