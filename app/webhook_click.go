package app

import (
	"context"
	"fmt"

	"billing_service/model"
	"billing_service/util"
	"billing_service/util/provider/click"
)

func (a *App) ClickWebHook(ctx context.Context, req click.WebhookRequest) (resp click.WebhookResponse) {
	invoice, err := a.repo.GetPaymentDetails(ctx, util.ParseInt(req.MerchantTransID))
	if err != nil {
		resp.SetInternalError(fmt.Sprintf("internal service error: %v", err))
		return
	}

	if invoice.Id == 0 {
		resp.SetTransactionNotFoundError(fmt.Sprintf("invoice %s not found", req.ClickTransID))
		return
	}

	if invoice.Amount != util.ParseInt(req.Amount) {
		resp.SetIncorrectAmountError(fmt.Sprintf("amount mismatch: expected %d, got %s", invoice.Amount, req.Amount))
		return
	}

	// Check for finished or cancelled states first
	if invoice.Status == model.PaymentStatusFinished {
		resp.SetTransactionAlreadyFinishedError("already finished transaction")
		return
	}

	if invoice.Status == model.PaymentStatusCancelledAfterFinish || invoice.Status == model.PaymentStatusCancelled {
		resp.SetTransactionAlreadyCancelledError("already cancelled transaction")
		return
	}

	switch req.Action {
	case click.ClickPrepareAction:
		if invoice.Status == model.PaymentStatusCreated {
			err = a.repo.SetPaymentInvoiceById(ctx, invoice.Id, req.ClickTransID)
			if err != nil {
				resp.SetUpdateTransactionError(fmt.Sprintf("update invoice by invoice id %d err %s", invoice.Id, err))
				return
			}

			err := a.repo.UpdatePaymentStatus(ctx, req.ClickTransID, model.PaymentStatusPending)
			if err != nil {
				resp.SetUpdateTransactionError(fmt.Sprintf("update transaction status for pending %s err %s", req.ClickTransID, err))
				return
			}
		}
		return click.WebhookResponse{
			ClickTransID:      util.ParseInt(req.ClickTransID),
			MerchantTransID:   req.MerchantTransID,
			MerchantPrepareID: invoice.Id,
			ErrorNote:         "success",
		}

	case click.ClickCompleteAction:
		if invoice.Status == model.PaymentStatusPending {
			err := a.repo.UpdatePaymentStatus(ctx, req.ClickTransID, model.PaymentStatusFinished)
			if err != nil {
				resp.SetUpdateTransactionError(fmt.Sprintf("update transaction status for finished %s err %s", req.ClickTransID, err))
				return
			}

			err = a.repo.UpdateOrderClientPaymentStatus(ctx, int(invoice.OrderId.Int64), model.OrderPaymentStatusSuccess)
			if err != nil {
				resp.SetUpdateTransactionError(fmt.Sprintf("update order payment status for %d err %s", invoice.OrderId.Int64, err))
				return
			}
		}
		return click.WebhookResponse{
			ClickTransID:      util.ParseInt(req.ClickTransID),
			MerchantTransID:   req.MerchantTransID,
			MerchantPrepareID: invoice.Id,
			ErrorNote:         "success",
		}

	default:
		resp.SetUnsupportedActionError(fmt.Sprintf("unsupported action: %d", req.Action))
		return
	}
}
