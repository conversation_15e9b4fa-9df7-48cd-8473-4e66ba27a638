package repo

import (
	"context"
	"fmt"
	"sync"

	"billing_service/repo/billing_click"
	"billing_service/repo/corp_service"
	"billing_service/repo/mysql"
	"billing_service/repo/postgres"
	"billing_service/repo/postgres_2"
	"billing_service/util/config"
	"billing_service/util/logger"
	"billing_service/util/nats"
)

var (
	r    *Repo
	once sync.Once
)

type Repo struct {
	log *logger.Logger
	*mysql.Mysql
	*postgres.Postgres
	*postgres_2.Postgres2
	*billing_click.BillingClick
	*corp_service.CorpService
	nats *nats.Client
}

func Get(ctx context.Context, cfg *config.Config, log *logger.Logger) *Repo {
	once.Do(func() {
		r = &Repo{
			log:          log,
			Mysql:        mysql.New(cfg, log),
			Postgres:     postgres.New(ctx, cfg.PaymentPostgres.GetDSN()),
			Postgres2:    postgres_2.New(ctx, cfg.BillingPostgres.GetDSN()),
			BillingClick: billing_click.New(cfg.Service.BillingClick.Host, cfg.Service.BillingClick.User, cfg.Service.BillingClick.Pass),
			CorpService:  corp_service.New(cfg.Service.CorpService.Host),
			nats:         nats.Get(cfg.Nats.Host, cfg.Nats.Token),
		}
	})

	return r
}

func (r *Repo) Close() error {
	err := r.Mysql.Close()
	if err != nil {
		return fmt.Errorf("error closing mysql database: %w", err)
	}

	err = r.Postgres.Close()
	if err != nil {
		return fmt.Errorf("error closing postgres database: %w", err)
	}

	err = r.Postgres2.Close()
	if err != nil {
		return fmt.Errorf("error closing postgres database: %w", err)
	}

	return nil
}
