package mysql

import (
	"strconv"
	"sync"
	"time"

	"billing_service/repo/mysql/sqls"
)

func (r *Mysql) GetSysParam(param, defaultValue string) *SysParamValue {
	return r.sysParams.GetValue(param, defaultValue)
}

func (r *Mysql) pollSysParams() {
	ticker := time.NewTicker(time.Second * time.Duration(r.cfg.Defaults.SysParamsPollInterval))

	for {
		r.log.Info("get system params...")
		err := r.getSysParams()
		if err != nil {
			r.log.Error("get system params: ", err)
		}
		<-ticker.C
	}
}

func (r *Mysql) getSysParams() (err error) {
	var sysParamsFromDb []SysParam

	err = r.db.Select(&sysParamsFromDb, sqls.GetSysParams)
	if err != nil {
		return
	}

	r.sysParams.Load(sysParamsFromDb)

	return
}

const maxSysParams = 1000

type SysParamValue string

type (
	SysParam struct {
		Param string        `db:"param"`
		Value SysParamValue `db:"value"`
	}

	SysParams struct {
		sync.RWMutex
		cache map[string]SysParamValue
	}
)

func NewSysParams() *SysParams {
	return &SysParams{
		cache: make(map[string]SysParamValue, maxSysParams),
	}
}

func (c *SysParams) Load(sysParams []SysParam) {
	c.Lock()
	defer c.Unlock()

	c.cache = make(map[string]SysParamValue, len(sysParams))

	for _, v := range sysParams {
		c.cache[v.Param] = v.Value
	}
}

func (c *SysParams) GetValue(param, defaultValue string) *SysParamValue {
	c.RLock()
	defer c.RUnlock()

	value, ok := c.cache[param]
	if !ok {
		value.Set(defaultValue)
	}
	return &value
}

func (v *SysParamValue) Set(value string) {
	*v = SysParamValue(value)
}

func (v SysParamValue) Str() string {
	return string(v)
}

func (v SysParamValue) Int64() int64 {
	value, _ := strconv.ParseInt(string(v), 10, 64)
	return value
}

func (v SysParamValue) Int() int {
	value, _ := strconv.Atoi(string(v))
	return value
}

func (v SysParamValue) Bool() bool {
	value, _ := strconv.ParseBool(string(v))
	return value
}

func (v SysParamValue) Float64() float64 {
	value, _ := strconv.ParseFloat(string(v), 64)
	return value
}
