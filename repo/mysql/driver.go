package mysql

import (
	"context"
	"database/sql"
	"fmt"

	"billing_service/model"
	"billing_service/repo/mysql/sqls"
)

func (r *Mysql) BlockDriver(ctx context.Context, driverId int) (resp bool, err error) {
	result, err := r.db.ExecContext(ctx, sqls.BlockDriver, driverId)
	if err != nil {
		return
	}
	affect, err := result.RowsAffected()
	if err != nil {
		return
	}
	if affect > 0 {
		resp = true
		_, _ = r.db.ExecContext(ctx, sqls.ActorActionDriverBlocked, driverId, "Низкий баланс")
	}
	return
}

func (r *Mysql) UnblockDriver(ctx context.Context, driverId int) (resp bool, err error) {
	result, err := r.db.ExecContext(ctx, sqls.UnblockDriver, driverId)
	if err != nil {
		return
	}
	affect, err := result.RowsAffected()
	if err != nil {
		return
	}
	if affect > 0 {
		resp = true
		_, _ = r.db.ExecContext(ctx, sqls.ActorActionDriverUnblocked, driverId, "Пополнение баланса")
	}
	return
}

func (r *Mysql) GetDriverExists(ctx context.Context, driverId int) (resp bool, err error) {
	err = r.db.GetContext(ctx, &resp, sqls.GetDriverExists, driverId)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

func (r *Mysql) GetDriverInfo(ctx context.Context, driverId int) (resp model.DriverInfo, err error) {
	err = r.db.GetContext(ctx, &resp, sqls.GetDriverInfo, driverId)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

func (r *Mysql) DriverMoneyTransferCreate(ctx context.Context, from, to, amount int) (transactionId int64, err error) {
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		err = fmt.Errorf("failed to begin transaction: %v", err)
		return
	}

	result, err := tx.ExecContext(ctx, sqls.DriverMoneyTransferCreate, from, to, amount)
	if err != nil {
		e := tx.Rollback()
		if e != nil {
			err = fmt.Errorf("failed to execute query and rollback transaction: %v, rollback error: %v", err, e)
			return
		}
		err = fmt.Errorf("failed to execute query: %v", err)
		return
	}

	transactionId, err = result.LastInsertId()
	if err != nil {
		e := tx.Rollback()
		if e != nil {
			err = fmt.Errorf("failed to retrieve last insert ID and rollback transaction: %v, rollback error: %v", err, e)
			return
		}
		err = fmt.Errorf("failed to retrieve last insert ID: %v", err)
		return
	}

	err = tx.Commit()

	return
}

func (r *Mysql) UpdateDriverMoneyTransferTransactionStatus(ctx context.Context, transactionId, status int) (err error) {
	_, err = r.db.ExecContext(ctx, sqls.UpdateDriverMoneyTransferTransactionStatus, status, transactionId)
	return
}

func (r *Mysql) GetDriverMoneyTransferStatus(ctx context.Context, transactionId int) (result int, err error) {
	err = r.db.GetContext(ctx, &result, sqls.GetDriverMoneyTransferStatus, transactionId)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}
