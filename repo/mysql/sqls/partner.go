package sqls

const GetDriverPartner = `
SELECT
	park.partner_id AS id,
	p.auto_payment,
	p.delay_period
FROM max_drivers d
JOIN max_taxi_parks park ON park.id = d.taxi_park_id
JOIN partners p ON p.id = park.partner_id
WHERE d.id = ?;`

const GetDriverPartnerA2A = `
SELECT
	p.id AS id,
	p.name,
	p.auto_payment,
	p.delay_period,
	p.inn,
	p.account,
	p.bank_code,
	p.a2a_queue_control
FROM partners p
WHERE p.id = ?;`

const GetAllPartners = `SELECT id, name FROM partners;`
