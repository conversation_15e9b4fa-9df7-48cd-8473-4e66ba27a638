package sqls

const GetOrderChosenPaymeCardId = `
SELECT c.card_id, o.client_id
FROM order_chosen_paycom_card c
JOIN max_taxi_incoming_orders o ON o.id = c.order_id
WHERE c.order_id = ?;`

const GetOrderInfo = `
SELECT
  o.client_id,
  od.total_client_cost,
  o.orderFrom,
  o.from_adres,
  o.to_adres,
  CAST(o.date AS char) AS date,
  CAST(o.client_sat_time AS char) AS client_sat_time,
  CAST(o.dr_arrived_time AS char) AS dr_arrived_time,
  CAST(od.end_time AS char) AS end_time,
  od.distance_to_client,
  od.distance_before_client_sat,
  od.driving_distance,
  car.name,
  car.number,
  car_color.name_ru,
  class.name_ru AS tarif_name,
  t.minimalka,
  t.vk_min,
  t.ojidaniy_minut,
  t.vgorode,
  t.vk_ojidaniy,
  od.interest_rate,
  od.waiting_time,
  od.paid_from_bonus,
  promo.code AS promocode,
  ST_X(o.coord_from_adres) AS coordinates_a_x,
  ST_Y(o.coord_from_adres) AS coordinates_a_y,
  ST_X(o.coord_to_adres) AS coordinates_b_x,
  ST_Y(o.coord_to_adres) AS coordinates_b_y,
  card.card_id
FROM max_taxi_incoming_orders AS o
LEFT JOIN max_taxi_orders_details AS od ON od.order_id = o.id
LEFT JOIN max_drivers AS driver ON driver.id = o.driver_id
LEFT JOIN max_units AS car ON car.id = driver.unit_id
LEFT JOIN max_taxi_car_colors AS car_color ON car_color.id = car.color_id
LEFT JOIN max_taxi_classes AS class ON class.id = o.tariffID
LEFT JOIN max_taxi_fleets_tarifs AS t ON t.class_id = o.tariffID
LEFT JOIN max_taxi_order_promo_details AS promo ON promo.order_id = o.id
LEFT JOIN order_chosen_paycom_card card ON card.order_id = o.id
WHERE o.id = ?;`

const GetOrderDriverId = `
SELECT driver_id FROM max_taxi_incoming_orders WHERE id = ?;`

const GetSuspiciousOrderInfo = `
SELECT 
    o.id,
    o.client_id,
    o.driver_id,
    od.total_client_cost
FROM max_taxi_incoming_orders AS o
JOIN max_taxi_orders_details AS od ON od.order_id = o.id
WHERE o.id = ?;`

const GetClientActiveOrdersByCardExists = `
SELECT EXISTS
(SELECT o.id
FROM max_taxi_incoming_orders o
JOIN order_chosen_paycom_card c ON c.order_id = o.id
WHERE o.client_id = ?
AND (o.status IS NULL OR o.status IN (1,5,6))
AND c.card_id = ?
LIMIT 1);`
