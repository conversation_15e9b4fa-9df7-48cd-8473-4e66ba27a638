package mysql

import (
	"fmt"
	"time"

	"billing_service/util/config"
	"billing_service/util/logger"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
)

type Mysql struct {
	cfg       *config.Config
	log       *logger.Logger
	db        *sqlx.DB
	sysParams *SysParams
}

func New(cfg *config.Config, log *logger.Logger) *Mysql {
	db := sqlx.MustConnect("mysql", cfg.Mysql.GetDSN())

	// Max open connections
	db.SetConnMaxLifetime(60 * time.Minute)
	db.SetConnMaxIdleTime(60 * time.Minute)
	db.SetMaxIdleConns(10)
	db.SetMaxOpenConns(10)

	m := &Mysql{
		cfg:       cfg,
		log:       log,
		db:        db,
		sysParams: NewSysParams(),
	}

	go m.pollSysParams()

	return m
}

func (r *Mysql) Close() error {
	err := r.db.Close()
	if err != nil {
		return fmt.Errorf("error closing mysql: %w", err)
	}

	return nil
}
