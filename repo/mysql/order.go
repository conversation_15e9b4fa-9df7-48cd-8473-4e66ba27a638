package mysql

import (
	"context"
	"database/sql"

	"billing_service/model"
	"billing_service/repo/mysql/sqls"
)

type clientCard struct {
	CardId   int `db:"card_id"`
	ClientId int `db:"client_id"`
}

func (r *Mysql) GetOrderChosenPaymeCardId(ctx context.Context, orderId int) (resp clientCard, err error) {
	err = r.db.GetContext(ctx, &resp, sqls.GetOrderChosenPaymeCardId, orderId)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

func (r *Mysql) GetOrderInfo(ctx context.Context, orderId int) (resp model.OrderInfo, err error) {
	err = r.db.GetContext(ctx, &resp, sqls.GetOrderInfo, orderId)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

func (r *Mysql) GetOrderDriverId(ctx context.Context, orderId int) (resp int, err error) {
	err = r.db.GetContext(ctx, &resp, sqls.GetOrderDriverId, orderId)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

func (r *Mysql) GetSuspiciousOrderInfo(ctx context.Context, orderId int) (resp model.SuspiciousOrderInfo, err error) {
	err = r.db.GetContext(ctx, &resp, sqls.GetSuspiciousOrderInfo, orderId)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

func (r *Mysql) GetClientActiveOrdersByCardExists(ctx context.Context, clientId, cardId int) (resp bool, err error) {
	err = r.db.GetContext(ctx, &resp, sqls.GetClientActiveOrdersByCardExists, clientId, cardId)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}
