package mysql

import (
	"context"
	"database/sql"

	"billing_service/model"
	"billing_service/repo/mysql/sqls"
)

func (r *Mysql) GetDriverPartner(ctx context.Context, driverId int) (resp model.Partner, err error) {
	err = r.db.GetContext(ctx, &resp, sqls.GetDriverPartner, driverId)
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

func (r *Mysql) GetPartnerA2A(ctx context.Context, partnerId int) (resp model.A2APartner, err error) {
	err = r.db.GetContext(ctx, &resp, sqls.GetDriverPartnerA2A, partnerId)
	if err != nil {
		if err == sql.ErrNoRows {
			err = nil
			return
		}
	}
	return
}

func (r *Mysql) GetAllPartners(ctx context.Context) (map[int]string, error) {
	resp := make(map[int]string)
	rows, err := r.db.QueryContext(ctx, sqls.GetAllPartners)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var id int
		var name string
		err = rows.Scan(&id, &name)
		if err != nil {
			continue
		}
		resp[id] = name
	}
	return resp, nil
}
