package postgres

import (
	"context"
	"time"

	"billing_service/repo/postgres/sqls"

	pgx "github.com/jackc/pgx/v5"
)

// GetCardPaymentAttempts returns the number of failed payment attempts for a card
// Also checks if a month has passed since the last reset and resets if needed
func (r *Postgres) GetCardPaymentAttempts(ctx context.Context, atmosHash string) (attemptCount int, err error) {
	var lastResetAt time.Time
	err = r.db.QueryRow(ctx, sqls.GetCardPaymentAttempts, atmosHash).Scan(&attemptCount, &lastResetAt)
	if err != nil {
		if err == pgx.ErrNoRows {
			err = nil
		}
		return
	}

	// Check if a month has passed since the last reset
	if time.Since(lastResetAt) > 30*24*time.Hour {
		// Reset the counter if a month has passed
		err = r.ResetCardPaymentAttempts(ctx, atmosHash)
		if err != nil {
			return
		}
		attemptCount = 0
	}

	return
}

// IncrementCardPaymentAttempts increments the number of failed payment attempts for a card
func (r *Postgres) IncrementCardPaymentAttempts(ctx context.Context, atmosHash string) (err error) {
	_, err = r.db.Exec(ctx, sqls.IncrementCardPaymentAttempts, atmosHash)
	return
}

// ResetCardPaymentAttempts resets the number of failed payment attempts for a card
func (r *Postgres) ResetCardPaymentAttempts(ctx context.Context, atmosHash string) (err error) {
	_, err = r.db.Exec(ctx, sqls.ResetCardPaymentAttempts, atmosHash)
	return
}

// ShouldResetMonthly checks if a month has passed since the last reset
func (r *Postgres) ShouldResetMonthly(ctx context.Context, atmosHash string) (shouldReset bool, err error) {
	err = r.db.QueryRow(ctx, sqls.CheckMonthlyReset, atmosHash).Scan(&shouldReset)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

// ResetAllCardPaymentAttempts resets all card payment attempts
// Returns the number of rows affected
func (r *Postgres) ResetAllCardPaymentAttempts(ctx context.Context) (count int64, err error) {
	result, err := r.db.Exec(ctx, sqls.ResetAllCardPaymentAttempts)
	if err != nil {
		return
	}
	return result.RowsAffected(), nil
}
