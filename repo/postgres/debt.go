package postgres

import (
	"context"

	"billing_service/model"
	"billing_service/repo/postgres/sqls"

	null "github.com/guregu/null/v6"
	pgx "github.com/jackc/pgx/v5"
)

func (r *Postgres) GetDebts(ctx context.Context, userId int, userType string) (resp []model.Debt, err error) {
	rows, err := r.db.Query(ctx, sqls.GetDebts, userId, userType)
	if err != nil {
		return
	}
	defer rows.Close()
	for rows.Next() {
		d := model.Debt{}
		err = rows.Scan(&d.OrderId, &d.Amount, &d.Type)
		if err != nil {
			return
		}
		if d.Type == "client" {
			d.Type = "personal"
		}
		resp = append(resp, d)
	}
	err = rows.Err()
	return
}

func (r *Postgres) GetDebtsTotalAmount(ctx context.Context, userId int, userType string) (resp null.Float, err error) {
	err = r.db.QueryRow(ctx, sqls.GetDebtsTotalAmount, userId, userType).Scan(&resp)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) CreateDebt(ctx context.Context, userId int, userType string, orderId, cardId, amount int) (err error) {
	_, err = r.db.Exec(ctx, sqls.CreateDebt, userId, userType, orderId, amount, cardId)
	return
}

func (r *Postgres) DeleteDebts(ctx context.Context, userId int, userType string) (err error) {
	_, err = r.db.Exec(ctx, sqls.DeleteDebts, userId, userType)
	return
}
