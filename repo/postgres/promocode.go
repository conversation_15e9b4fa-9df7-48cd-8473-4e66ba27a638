package postgres

import (
	"billing_service/model"
	"billing_service/repo/postgres/sqls"
	"context"

	null "github.com/guregu/null/v6"
)

func (r *Postgres) GetPromocodes(ctx context.Context, clientId int, isNewClient bool) (resp []model.Promocode, err error) {
	rows, err := r.db.Query(ctx, sqls.GetPromocodes, clientId, isNewClient)
	if err != nil {
		return
	}
	defer rows.Close()
	for rows.Next() {
		p := model.Promocode{}
		err = rows.Scan(&p.Code, &p.ExpireTime, &p.Type, &p.Value, &p.Limit, &p.DiscountLimit, &p.ValidDays)
		if err != nil {
			return
		}
		resp = append(resp, p)
	}
	err = rows.Err()
	return
}

func (r *Postgres) XpanelGetPromocodes(ctx context.Context, code string, clientId int, isActive null.Bool, page, limit int) (resp []model.XpanelPromocode, err error) {
	offset := 0
	if page > 1 {
		offset = (page - 1) * limit
	}

	rows, err := r.db.Query(ctx, sqls.XpanelGetPromocodes, code, clientId, limit, offset)
	if err != nil {
		return
	}
	defer rows.Close()
	for rows.Next() {
		p := model.XpanelPromocode{}
		err = rows.Scan(&p.Code, &p.CreateTime, &p.ExpireTime, &p.Type, &p.Value, &p.Limit, &p.DiscountLimit, &p.IsForNewClients, &p.ClientId, &p.IsActive, &p.ValidDays)
		if err != nil {
			return
		}
		resp = append(resp, p)
	}
	err = rows.Err()
	return
}

func (r *Postgres) XpanelGetPromocodesCount(ctx context.Context, code string, clientId int) (count int, err error) {
	err = r.db.QueryRow(ctx, sqls.XpanelGetPromocodesCount, code, clientId).Scan(&count)
	return
}

func (r *Postgres) XpanelAddPromocode(ctx context.Context, req model.XpanelPromocode) (err error) {
	_, err = r.db.Exec(ctx, sqls.XpanelAddPromocode, req.Code, req.CreateTime, req.ExpireTime, req.Type, req.Value, req.Limit, req.DiscountLimit, req.ClientId, req.IsForNewClients, req.ValidDays)
	return
}

func (r *Postgres) XpanelUpdatePromocode(ctx context.Context, code string, clientId null.Int, isActive null.Bool) (err error) {
	_, err = r.db.Exec(ctx, sqls.XpanelUpdatePromocode, code, clientId, isActive)
	return
}
