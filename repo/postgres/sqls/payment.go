package sqls

const CreatePayment = `
INSERT INTO payments (user_id, user_type, card_id, order_id, amount, invoice, provider_id, reason, status, is_p2p)
VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)
ON CONFLICT (invoice) DO UPDATE SET status = $9;`

const UpdatePaymentStatus = `
UPDATE payments SET status = $2, updated_at = NOW() WHERE invoice = $1;`

const GetPaymentStatusByOrderId = `
SELECT
	invoice,
	status
FROM payments
WHERE order_id = $1 AND reason = $2
ORDER BY created_at DESC
LIMIT 1;`

const GetPaymentStatusByInvoice = `
SELECT
	invoice,
	status
FROM payments
WHERE invoice = $1;`

const GetPaymentDetails = `
SELECT
	p.id,
	p.order_id,
	p.card_id,
	p.invoice,
	p.amount,
	p.status,
	p.provider_id,
	p.is_p2p,
	o.client_id,
	o.driver_id
FROM payments p
LEFT JOIN payments_orders o ON o.id = p.order_id
WHERE p.id = $1;`

const GetPaymentDetailsByInvoice = `
SELECT
	p.id,
	p.user_id,
	p.order_id,
	p.card_id,
	p.invoice,
	p.amount,
	p.status,
	p.reason,
	p.provider_id,
	p.is_p2p,
	o.client_id,
	o.driver_id
FROM payments p
LEFT JOIN payments_orders o ON o.id = p.order_id
WHERE p.invoice = $1;`

const GetPaynetPayments = `
SELECT
    id,
	invoice::int8,
	amount,
	updated_at
FROM payments
WHERE provider_id = 4
AND updated_at >= $1 AND updated_at < $2
AND status != 2
ORDER BY updated_at ASC;`

const CreatePartnerA2ATransaction = `
INSERT INTO partner_transaction (order_id, partner_id, amount, total_sum, percentage, provider_type, idempotency_key, status, reason, log)
VALUES ($1, $2, $3, $4, $5, $6, gen_random_uuid()::text, $7, 1, $8)
ON CONFLICT (order_id) DO NOTHING;`

const SetPaymentInvoiceById = `
UPDATE payments SET invoice = $1 WHERE id = $2`

const GetPaymentByInvoice = `
SELECT
	id,
	user_id,
	user_type,
	card_id,
	order_id,
	amount,
	status,
	reason,
	provider_id,
	invoice,
	is_p2p
FROM payments
WHERE invoice = $1;`

const UpdatePartnerPaymentStatus = `
UPDATE partner_transaction SET status = $2, comment = $3, updated_at = NOW() WHERE id = $1;`
