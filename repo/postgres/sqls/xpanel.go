package sqls

const XpanelGetClientVisaPayments = `
SELECT 
    p.id,
    p.amount,
    p.invoice,
    p.status,
    TO_CHAR(p.created_at, 'yyyy-mm-ddThh24:mi:ss') AS created_at,
	TO_CHAR(p.updated_at, 'yyyy-mm-ddThh24:mi:ss') AS updated_at,
	c.id,
	c.number
FROM payments p
JOIN cards c ON p.card_id = c.id
WHERE p.order_id = $1 and p.reason = 6;`

const XpanelGetClientPaymePayments = `
SELECT
	p.id,
	p.order_id,
	p.amount,
	p.status,
	p.is_p2p,
	p.invoice,
	TO_CHAR(p.created_at, 'yyyy-mm-ddThh24:mi:ss') AS created_at,
	c.id,
	c.number,
	c.expire
FROM payments p
LEFT JOIN cards c ON c.id = p.card_id
WHERE p.order_id = $1 AND p.provider_id = 1 AND p.reason IN(6, 8);`

const XpanelGetDriverA2CPayments = `
SELECT
	p.order_id,
	p.user_id,
	p.amount,
	p.status,
	p.provider_id,
	p.invoice,
	TO_CHAR(p.created_at, 'yyyy-mm-ddThh24:mi:ss') AS created_at,
	TO_CHAR(p.updated_at, 'yyyy-mm-ddThh24:mi:ss') AS updated_at,
	c.number
FROM payments p
LEFT JOIN cards c ON c.id = p.card_id
WHERE p.order_id = $1 AND p.reason IN (7,9,13);`

const XpanelGetOrderIsSuspicious = `
SELECT EXISTS (SELECT * FROM payments_orders WHERE id = $1 AND driver_payment_status = 7 LIMIT 1);`

const XpanelUpdateClientDebtStatus = `
UPDATE debts SET is_paid = $2 WHERE id = $1;`

const XpanelGetClientDebtsCount = `
SELECT
	COUNT(*) AS count
FROM debts
WHERE user_type = 'client'
AND ($1 = 0 OR user_id = $1)
AND ($2 = '' OR user_phone = $2);`

const XpanelGetClientDebts = `
SELECT
	id,
	user_id,
	user_phone,
	order_id,
	amount,
	COALESCE(payme_hash, atmos_hash),
	TO_CHAR(created_at, 'yyyy-mm-ddThh24:mi:ss'),
	TO_CHAR(updated_at, 'yyyy-mm-ddThh24:mi:ss'),
	is_paid
FROM debts
WHERE user_type = 'client'
AND ($1 = 0 OR user_id = $1)
AND ($2 = 0 OR order_id = $2)
AND ($3 = '' OR user_phone = $3)
ORDER BY id DESC
LIMIT $4 OFFSET $5;`

const XpanelCreateDebt = `
INSERT INTO debts (order_id, user_id, user_type, amount)
VALUES ($1,$2,$3,$4)
ON CONFLICT (order_id) DO UPDATE SET is_paid = false, amount = $4, created_at = NOW(), updated_at = NOW();`

const XpanelGetClientCardsCount = `
SELECT COUNT(*) 
FROM cards 
WHERE user_type = 'client'
AND ($1 = 0 OR user_id = $1)
AND ($2 = '' OR payme_hash = $2)
AND is_deleted = $3;`

const XpanelGetClientCards = `
SELECT 
    user_id,
    number,
    expire,
    COALESCE(payme_hash, atmos_hash),
    is_deleted
FROM cards
WHERE user_type = 'client'
AND ($1 = 0 OR user_id = $1)
AND ($2 = '' OR payme_hash = $2)
AND is_deleted = $3
ORDER BY created_at DESC
LIMIT $4 OFFSET $5;`

const XpanelGetDriverCardsCount = `
SELECT COUNT(*) 
FROM cards
WHERE user_type = 'driver'
AND ($1 = 0 OR user_id = $1)
AND is_deleted = $2;`

const XpanelGetDriverCards = `
SELECT 
    user_id,
    number,
    expire,
    created_at,
	updated_at,
    is_deleted
FROM cards
WHERE user_type = 'driver'
AND ($1 = 0 OR user_id = $1)
AND is_deleted = $2
ORDER BY created_at DESC
LIMIT $3 OFFSET $4;`
