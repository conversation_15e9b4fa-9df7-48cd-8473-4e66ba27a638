package sqls

const GetWaitingPartners = `
SELECT DISTINCT(partner_id) 
FROM partner_transaction
WHERE status = 30 AND created_at < NOW() - $1::INTERVAL;`

const CreatePartnerTransfer = `
INSERT INTO partner_transfer (partner_id, amount, idempotency_key, provider_type, status)
VALUES ($1, $2, $3, $4, 30)
RETURNING id, partner_id, amount, status, invoice, idempotency_key, provider_type;`

const FetchA2APartnerTransfers = `
SELECT id, partner_id, amount, status, invoice, idempotency_key, provider_type
FROM partner_transfer
WHERE id = $1;`

const UpdateA2ATransactionsIdpKey = `
UPDATE partner_transaction
SET idempotency_key = $1
WHERE transfer_id = $2;`

const UpdateA2ATransferIdpKey = `
UPDATE partner_transfer
SET idempotency_key = $1
WHERE id = $2;`
