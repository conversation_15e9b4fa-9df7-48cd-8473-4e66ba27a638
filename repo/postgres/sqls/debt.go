package sqls

const GetDebts = `
SELECT
	order_id,
	amount,
	user_type
FROM debts
WHERE user_id = $1
AND user_type = $2
AND is_paid = FALSE;`

const GetClientDebts = `
SELECT
	order_id,
	amount,
	user_type
FROM debts
WHERE user_id = $1
AND user_type = $2
AND is_paid = FALSE
UNION
SELECT
	d.order_id,
	d.amount,
	c.user_type
FROM cards c
JOIN debts d ON d.payme_hash = c.payme_hash
WHERE c.user_id = $1
AND c.user_type = $2
AND c.payme_hash IS NOT NULL
AND d.is_paid = FALSE;`

const GetDebtsTotalAmount = `
SELECT
	amount
FROM (
	SELECT d.amount
	FROM debts d
	LEFT JOIN cards c ON c.payme_hash = d.payme_hash
	AND c.user_id = $1
	AND c.user_type = $2
	WHERE ((d.user_id = $1 AND d.user_type = $2) OR c.id IS NOT NULL) AND d.is_paid = FALSE
) AS debts;`

// const GetDebtsTotalAmount = `
// SELECT
// 	SUM(amount)
// FROM debts
// WHERE user_id = $1
// AND user_type = $2
// AND is_paid = FALSE;`

const CreateDebt = `
INSERT INTO debts (user_id, user_type, order_id, amount, payme_hash, atmos_hash)
SELECT $1,$2,$3,$4,payme_hash,atmos_hash FROM cards WHERE id = $5
ON CONFLICT (order_id) DO NOTHING;`

const DeleteDebts = `
UPDATE debts
SET is_paid = TRUE, updated_at = NOW()
WHERE user_id = $1
AND user_type = $2
AND is_paid = FALSE;`
