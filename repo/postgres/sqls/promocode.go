package sqls

const GetPromocodes = `
SELECT
	code,
	expired_at,
	type,
	value,
	"limit",
	discount_limit,
	valid_days
FROM promocodes
WHERE is_active = TRUE
AND expired_at > NOW()
AND (client_id IS NULL OR client_id = $1)
AND (is_for_new_clients = FALSE OR is_for_new_clients = $2);`

const XpanelGetPromocodes = `
SELECT
	code,
	created_at,
	expired_at,
	type,
	value,
	"limit",
	discount_limit,
	is_for_new_clients,
	client_id,
	is_active,
	valid_days
FROM promocodes
WHERE ($1 = '' OR code = $1)
AND ($2 = 0 OR client_id = $2)
LIMIT $3 OFFSET $4;`

// AND ($3 IS NULL OR is_active = $3);`

const XpanelAddPromocode = `
INSERT INTO promocodes (code, created_at, expired_at, type, value, "limit", discount_limit, client_id, is_for_new_clients, valid_days)
VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)
ON CONFLICT (code) DO NOTHING;`

const XpanelUpdatePromocode = `
UPDATE promocodes SET
client_id = COALESCE($2, client_id),
is_active = COALESCE($3, is_active)
WHERE code = $1;`
