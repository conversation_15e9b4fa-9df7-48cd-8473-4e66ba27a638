package sqls

const GetCard = `
SELECT
	id,
	number,
    full_number,
	expire,
	brand,
    category,
    payme_token,
    payme_hash,
    atmos_token,
    atmos_hash
FROM cards
WHERE id = $1
AND is_deleted = FALSE;`

const GetCardById = `
SELECT
	id,
	number,
    full_number,
	expire,
	brand,
    category,
    payme_token,
    payme_hash,
    atmos_token,
    atmos_hash
FROM cards
WHERE id = $1 AND user_id = $2 AND user_type = $3
AND is_deleted = FALSE;`

const GetCardByUserId = `
SELECT
	id,
	number,
    full_number,
	expire,
	brand,
    category,
    payme_token,
    payme_hash,
    atmos_token,
    atmos_hash
FROM cards
WHERE user_id = $1
AND user_type = $2
AND is_deleted = FALSE
ORDER BY created_at DESC
LIMIT 1;`

const GetCardsByUserId = `
SELECT
	id,
	number,
    full_number,
	expire,
	brand,
    category,
    payme_token,
    payme_hash,
    atmos_token,
    atmos_hash,
    checked_at
FROM cards
WHERE user_id = $1
AND user_type = $2
AND is_deleted = FALSE
ORDER BY id;`

const GetCardIdByPaymeHash = `
SELECT
	id
FROM cards
WHERE user_id = $1
AND user_type = $2
AND payme_hash = $3
LIMIT 1;`

const UpdatePaymeCard = `
UPDATE cards
SET
    number = $1,
    full_number = $2,
    expire = $3,
    brand = $4,
    payme_token = $5,
    payme_hash = $6,
    is_deleted = FALSE,
    updated_at = NOW()
WHERE id = $7;`

const UpdateCardCheckedTime = `
UPDATE cards
SET checked_at = NOW()
WHERE id = $1;`

const DeleteCard = `
UPDATE cards
SET payme_token = NULL, atmos_token = NULL, is_deleted = TRUE, updated_at = NOW()
WHERE id = $1
AND user_id = $2
AND user_type = $3;`

const AddPaymeCard = `
INSERT INTO cards (user_id, user_type, number, full_number, expire, brand, category, payme_token, payme_hash)
VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9)
ON CONFLICT (payme_token) DO UPDATE SET is_deleted = FALSE;`

const GetCardBrand = `
SELECT
    br.brand,
    br.category
FROM card_brands br
JOIN card_bins bi ON br.id = bi.brand_id
WHERE bi.bin = $1
LIMIT 1;`

const GetCardIdByAtmosHash = `
SELECT
	id
FROM cards
WHERE user_id = $1
AND user_type = $2
AND atmos_hash = $3
LIMIT 1;`

const AddAtmosCard = `
INSERT INTO cards (user_id, user_type, number, brand, category, atmos_token, atmos_hash)
VALUES ($1,$2,$3,$4,$5,$6,$7)
ON CONFLICT (atmos_token) DO UPDATE SET is_deleted = FALSE;`

const UpdateAtmosCard = `
UPDATE cards
SET
    number = $1,
    expire = $2,
    brand = $3,
    category = $4,
    atmos_token = $5,
    atmos_hash = $6,
    is_deleted = FALSE,
    updated_at = NOW()
WHERE id = $7;`
