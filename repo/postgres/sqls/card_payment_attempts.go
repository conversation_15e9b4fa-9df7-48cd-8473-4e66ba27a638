package sqls

const GetCardPaymentAttempts = `
SELECT
    attempt_count,
    last_reset_at
FROM card_payment_attempts
WHERE atmos_hash = $1;`

const IncrementCardPaymentAttempts = `
INSERT INTO card_payment_attempts (atmos_hash, attempt_count, last_attempt_at)
VALUES ($1, 1, NOW())
ON CONFLICT (atmos_hash) DO UPDATE
SET attempt_count = card_payment_attempts.attempt_count + 1,
    last_attempt_at = NOW(),
    updated_at = NOW();`

const ResetCardPaymentAttempts = `
UPDATE card_payment_attempts
SET attempt_count = 0,
    last_attempt_at = NOW(),
    last_reset_at = NOW(),
    updated_at = NOW()
WHERE atmos_hash = $1;`

const CheckMonthlyReset = `
SELECT
    CASE
        WHEN last_reset_at < NOW() - INTERVAL '1 month' THEN TRUE
        ELSE FALSE
    END as should_reset
FROM card_payment_attempts
WHERE atmos_hash = $1;`

const ResetAllCardPaymentAttempts = `
UPDATE card_payment_attempts
SET attempt_count = 0,
    last_attempt_at = NOW(),
    last_reset_at = NOW(),
    updated_at = NOW();`
