package postgres

import (
	"context"

	"billing_service/model"
	"billing_service/repo/postgres/sqls"

	null "github.com/guregu/null/v6"
	pgx "github.com/jackc/pgx/v5"
)

func (r *Postgres) AddPaymeCard(ctx context.Context, userId int, userType string, card model.Card) (err error) {
	if card.PaymeHash.String != "" {
		var cardId int

		err = r.db.QueryRow(ctx, sqls.GetCardIdByPaymeHash, userId, userType, card.PaymeHash.String).Scan(&cardId)
		if err != nil && err != pgx.ErrNoRows {
			return
		}

		if cardId > 0 {
			_, err = r.db.Exec(ctx, sqls.UpdatePaymeCard, card.Number, card.FullNumber, card.Expire, card.Brand, card.PaymeToken, card.PaymeHash, cardId)
			return
		}
	}

	_, err = r.db.Exec(ctx, sqls.AddPaymeCard, userId, userType, card.Number, card.FullNumber, card.Expire, card.Brand, card.Category, card.PaymeToken, card.PaymeHash)

	return
}

func (r *Postgres) AddAtmosCard(ctx context.Context, userId int, userType string, card model.Card) (err error) {
	if card.AtmosHash.String != "" {
		var cardId int

		err = r.db.QueryRow(ctx, sqls.GetCardIdByAtmosHash, userId, userType, card.AtmosHash.String).Scan(&cardId)
		if err != nil && err != pgx.ErrNoRows {
			return
		}

		if cardId > 0 {
			_, err = r.db.Exec(ctx, sqls.UpdateAtmosCard, card.Number, card.Expire, card.Brand, card.Category, card.AtmosToken, card.AtmosHash, cardId)
			return
		}
	}

	_, err = r.db.Exec(ctx, sqls.AddAtmosCard, userId, userType, card.Number, card.Brand, card.Category, card.AtmosToken, card.AtmosHash)
	return
}

func (r *Postgres) GetCard(ctx context.Context, id int) (c model.Card, err error) {
	err = r.db.QueryRow(ctx, sqls.GetCard, id).Scan(&c.Id, &c.Number, &c.FullNumber, &c.Expire, &c.Brand, &c.Category, &c.PaymeToken, &c.PaymeHash, &c.AtmosToken, &c.AtmosHash)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) GetCardById(ctx context.Context, id, userId int, userType string) (c model.Card, err error) {
	err = r.db.QueryRow(ctx, sqls.GetCardById, id, userId, userType).Scan(&c.Id, &c.Number, &c.FullNumber, &c.Expire, &c.Brand, &c.Category, &c.PaymeToken, &c.PaymeHash, &c.AtmosToken, &c.AtmosHash)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) GetCardByUserId(ctx context.Context, userId int, userType string) (c model.Card, err error) {
	err = r.db.QueryRow(ctx, sqls.GetCardByUserId, userId, userType).Scan(&c.Id, &c.Number, &c.FullNumber, &c.Expire, &c.Brand, &c.Category, &c.PaymeToken, &c.PaymeHash, &c.AtmosToken, &c.AtmosHash)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) GetCardsByUserId(ctx context.Context, userId int, userType string) (resp []model.Card, err error) {
	rows, err := r.db.Query(ctx, sqls.GetCardsByUserId, userId, userType)
	if err != nil {
		if err == pgx.ErrNoRows {
			err = nil
		}
		return
	}
	defer rows.Close()
	for rows.Next() {
		c := model.Card{}
		err = rows.Scan(&c.Id, &c.Number, &c.FullNumber, &c.Expire, &c.Brand, &c.Category, &c.PaymeToken, &c.PaymeHash, &c.AtmosToken, &c.AtmosHash, &c.CheckedAt)
		if err != nil {
			return
		}
		resp = append(resp, c)
	}
	err = rows.Err()
	return
}

func (r *Postgres) UpdateCardCheckedTime(ctx context.Context, cardId int) (err error) {
	_, err = r.db.Exec(ctx, sqls.UpdateCardCheckedTime, cardId)
	return
}

func (r *Postgres) DeleteCard(ctx context.Context, cardId int, userId int, userType string) (err error) {
	_, err = r.db.Exec(ctx, sqls.DeleteCard, cardId, userId, userType)
	return
}

func (r *Postgres) GetCardBrand(ctx context.Context, bin string) (brand string, category null.String, err error) {
	err = r.db.QueryRow(ctx, sqls.GetCardBrand, bin).Scan(&brand, &category)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}
