package postgres

import (
	"context"

	"github.com/jackc/pgx/v5/pgxpool"
)

type Postgres struct {
	db *pgxpool.Pool
}

func New(ctx context.Context, dsn string) *Postgres {
	db, err := pgxpool.New(ctx, dsn)
	if err != nil {
		panic(err)
	}

	err = db.Ping(ctx)
	if err != nil {
		panic(err)
	}

	return &Postgres{
		db: db,
	}
}

func (r *Postgres) Close() error {
	if r.db != nil {
		r.db.Close()
	}
	return nil
}
