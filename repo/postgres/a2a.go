package postgres

import (
	"context"
	"fmt"
	"strings"
	"time"

	"billing_service/model"
	"billing_service/repo/postgres/sqls"

	"github.com/google/uuid"
	"github.com/guregu/null/v6"
	"github.com/lib/pq"
)

var (
	PartnerTransaction = "partner_transaction" // a2a record for order
	_                  = "partner_transfer"    // the group of partner transactions as single invoice
)

func (r *Postgres) FetchA2A(ctx context.Context, filterParams map[string]any, limit, offset int) ([]model.A2ATransactionView, error) {
	var resp = make([]model.A2ATransactionView, 0)
	script := `
SELECT
	id,
	created_at,
	partner_id,
	order_id,
	amount,
	invoice,
	idempotency_key,
	status,
	reason,
	provider_type,
	comment,
	percentage,
	total_sum,
	transfer_id
FROM %s
WHERE %s ORDER BY created_at DESC %s %s ;`

	var argCount int
	var argsList []any
	var whereClauses []string
	var whereScript string
	var limitScript string
	var offsetScript string

	for column, value := range filterParams {
		if column == "transfer_id" && value == nil {
			whereClauses = append(whereClauses, "transfer_id IS NULL")
		} else {
			argCount++
			if column == "from_date" {
				whereClauses = append(whereClauses, fmt.Sprintf("%s > $%d", "created_at", argCount))
			} else if column == "to_date" {
				whereClauses = append(whereClauses, fmt.Sprintf("%s < $%d", "created_at", argCount))
			} else {
				whereClauses = append(whereClauses, fmt.Sprintf("%s=$%d", column, argCount))
			}
			argsList = append(argsList, value)
		}
	}

	if len(whereClauses) > 0 {
		whereScript = strings.Join(whereClauses, " AND ")
	} else {
		whereScript = "1=1"
	}

	if limit > 0 {
		argCount++
		limitScript = fmt.Sprintf(" LIMIT $%d ", argCount)
		argsList = append(argsList, limit)
	}
	if offset > 0 {
		argCount++
		offsetScript = fmt.Sprintf(" OFFSET $%d ", argCount)
		argsList = append(argsList, offset)
	}
	finalQuery := fmt.Sprintf(script, PartnerTransaction, whereScript, limitScript, offsetScript)

	rows, err := r.db.Query(ctx, finalQuery, argsList...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var item model.A2ATransactionView
		if err := rows.Scan(
			&item.Id,
			&item.CreatedAt,
			&item.PartnerId,
			&item.OrderId,
			&item.Amount,
			&item.Invoice,
			&item.IdpKey,
			&item.Status,
			&item.Reason,
			&item.ProviderType,
			&item.Comment,
			&item.Percentage,
			&item.TotalSum,
			&item.TransferId,
		); err != nil {
			return nil, fmt.Errorf("failed to scan row: %s", err)
		}
		resp = append(resp, item)
	}

	return resp, nil
}

func (r *Postgres) GetCount(ctx context.Context, filterParams map[string]any) (int, error) {
	var count int
	script := `
	SELECT
		COUNT(*)
	FROM partner_transaction
	WHERE %s
		`
	var argCount int
	var argsList []any
	var whereClauses []string
	var whereScript string

	for column, value := range filterParams {
		argCount++
		if column == "from_date" {
			whereClauses = append(whereClauses, fmt.Sprintf("%s > $%d", "created_at", argCount))
		} else if column == "to_date" {
			whereClauses = append(whereClauses, fmt.Sprintf("%s < $%d", "created_at", argCount))
		} else {
			whereClauses = append(whereClauses, fmt.Sprintf("%s=$%d", column, argCount))
		}
		argsList = append(argsList, value)
	}

	if len(whereClauses) > 0 {
		whereScript = strings.Join(whereClauses, " AND ")
	} else {
		whereScript = "1=1"
	}

	err := r.db.QueryRow(ctx, fmt.Sprintf(script, whereScript), argsList...).Scan(&count)
	if err != nil {
		fmt.Printf("error on getting a2a list len: %s", err)
	}
	return count, err
}

func (r *Postgres) UpdateA2A(ctx context.Context, tableName string, updateParams, filterParams map[string]any) (err error) {
	if len(updateParams) == 0 {
		return
	}
	if len(filterParams) == 0 {
		return
	}

	var setClauses []string
	var args []any
	argIndex := 1

	for field, value := range updateParams {
		setClauses = append(setClauses, fmt.Sprintf("%s=$%d", field, argIndex))
		args = append(args, value)
		argIndex++
	}

	var whereClauses []string
	for field, value := range filterParams {
		whereClauses = append(whereClauses, fmt.Sprintf("%s=$%d", field, argIndex))
		args = append(args, value)
		argIndex++
	}

	query := fmt.Sprintf(
		"UPDATE %s SET %s WHERE %s", tableName, strings.Join(setClauses, ", "), strings.Join(whereClauses, " AND "),
	)

	_, err = r.db.Exec(ctx, query, args...)

	return
}

func (r *Postgres) GetA2AByTransactionId(ctx context.Context, transactionId int) (resp []model.A2ATransactionView, err error) {
	return r.FetchA2A(ctx, map[string]any{"id": transactionId}, 0, 0)
}

func (r *Postgres) GetA2ATransactionByFilter(ctx context.Context, filterParams map[string]any) (resp []model.A2ATransactionView, err error) {
	return r.FetchA2A(ctx, filterParams, 0, 0)
}

func (r *Postgres) GetA2AByOrderId(ctx context.Context, orderId int) (resp []model.A2ATransactionView, err error) {
	return r.FetchA2A(ctx, map[string]any{"order_id": orderId}, 0, 0)
}

func (r *Postgres) CancelA2ATransaction(ctx context.Context, transactionId int, comment null.String) (err error) {
	_, err = r.db.Exec(ctx, sqls.UpdatePartnerPaymentStatus, transactionId, 50, comment)
	return
}

func (r *Postgres) UpdateA2AAmount(ctx context.Context, transactionId, amount int, comment null.String) error {
	filterParams := map[string]any{"id": transactionId}
	updateParams := map[string]any{"amount": amount, "comment": comment}

	return r.UpdateA2A(ctx, PartnerTransaction, updateParams, filterParams)
}

func (r *Postgres) FilterA2AForListView(ctx context.Context, filterParams map[string]any, limit, offset int) ([]model.A2ATransactionView, int, error) {
	resp, err := r.FetchA2A(ctx, filterParams, limit, offset)
	if err != nil {
		return resp, 0, err
	}

	count, err := r.GetCount(ctx, filterParams)
	if err != nil {
		fmt.Printf("error happened on getting a2a count: %s", err.Error())
		count = 0
	}

	return resp, count, nil
}

func (r *Postgres) GetWaitingPartners(ctx context.Context, delayPeriod int) (resp []int, err error) {
	rows, err := r.db.Query(ctx, sqls.GetWaitingPartners, fmt.Sprintf("%d", delayPeriod))
	if err != nil {
		return
	}
	for rows.Next() {
		var p int
		err = rows.Scan(&p)
		if err != nil {
			return
		}
		resp = append(resp, p)
	}

	err = rows.Err()

	return
}

func (r *Postgres) CancelA2A(ctx context.Context, filter, update map[string]any) error {
	return r.UpdateA2A(ctx, PartnerTransaction, update, filter)
}

func (r *Postgres) CreatePartnerTransferRecord(ctx context.Context, partnerId, amount int, idpKey string, provider int) (resp model.A2APartnerTransfer, err error) {
	err = r.db.QueryRow(ctx, sqls.CreatePartnerTransfer, partnerId, amount, idpKey, provider).Scan(&resp.Id, &resp.PartnerId, &resp.Amount, &resp.Status, &resp.Invoice, &resp.IdempotencyKey, &resp.ProviderType)
	return resp, err
}

func (r *Postgres) GetA2APartnerTransferRecord(ctx context.Context, id int) (resp model.A2APartnerTransfer, err error) {
	err = r.db.QueryRow(ctx, sqls.FetchA2APartnerTransfers, id).Scan(&resp.Id, &resp.PartnerId, &resp.Amount, &resp.Status, &resp.Invoice, &resp.IdempotencyKey, &resp.ProviderType)
	return resp, err
}

func (r *Postgres) extractTrIds(transactions []model.A2ATransactionView) []int {
	resp := make([]int, 0)
	for _, transaction := range transactions {
		resp = append(resp, transaction.Id)
	}
	return resp
}

// UpdateBulkA2A []model.A2ATransactionView actions: update, calculate total sum
func (r *Postgres) UpdateBulkA2A(ctx context.Context, transactions []model.A2ATransactionView, updateParams map[string]any) error {
	trIds := r.extractTrIds(transactions)
	if len(trIds) == 0 {
		return nil
	}

	var setClauses = make([]string, 0)
	var args = make([]any, 0)
	argIndex := 1

	for field, value := range updateParams {
		setClauses = append(setClauses, fmt.Sprintf("%s=$%d", field, argIndex))
		args = append(args, value)
		argIndex++
	}

	args = append(args, pq.Array(trIds))

	query := fmt.Sprintf(
		"UPDATE %s SET %s WHERE id = ANY($%d)",
		PartnerTransaction,
		strings.Join(setClauses, ", "),
		argIndex,
	)

	_, err := r.db.Exec(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("failed to execute bulk update: %w", err)
	}

	return nil
}

func (r *Postgres) BulkCancelA2A(ctx context.Context, transactions []model.A2ATransactionView) error {
	return r.UpdateBulkA2A(ctx,
		transactions,
		map[string]any{"status": 50, "comment": "Cancelled by queue param", "log": "Cancelled for partner"},
	)
}

func (r *Postgres) SetTransferInfoToTransactions(ctx context.Context, transactions []model.A2ATransactionView, transfer model.A2APartnerTransfer) error {
	return r.UpdateBulkA2A(ctx,
		transactions,
		map[string]any{"transfer_id": transfer.Id, "status": transfer.Status, "idempotency_key": transfer.IdempotencyKey, "updated_at": time.Now()},
	)
}

func (r *Postgres) RemoveTransferIdFromTransactions(ctx context.Context, transactions []model.A2ATransactionView) error {
	return r.UpdateBulkA2A(ctx, transactions, map[string]any{"transfer_id": nil})
}

func (r *Postgres) ReleaseA2A(ctx context.Context, transferId int, log string) (err error) {
	err = r.UpdateA2A(ctx,
		"partner_transfer",
		map[string]any{"status": 50, "log": log, "updated_at": time.Now()},
		map[string]any{"id": transferId},
	)
	if err != nil {
		err = fmt.Errorf("couldnt update partner transfer: %v", err)
		return
	}
	err = r.UpdateA2A(ctx,
		"partner_transaction",
		map[string]any{"status": 30, "transfer_id": nil, "log": log, "updated_at": time.Now()},
		map[string]any{"transfer_id": transferId},
	)
	if err != nil {
		err = fmt.Errorf("couldnt update partner transaction: %v", err)
		return
	}
	return
}

func (r *Postgres) DelayA2A(ctx context.Context, transferId int, log string) (err error) {
	err = r.UpdateA2A(ctx,
		"partner_transfer",
		map[string]any{"status": 50, "updated_at": time.Now(), "log": log},
		map[string]any{"id": transferId},
	)
	if err != nil {
		err = fmt.Errorf("couldnt update partner transfer: %v", err)
		return
	}
	err = r.UpdateA2A(ctx,
		"partner_transaction",
		map[string]any{"status": 30, "transfer_id": nil, "log": log, "updated_at": time.Now()},
		map[string]any{"transfer_id": transferId},
	)
	if err != nil {
		err = fmt.Errorf("couldnt update partner transaction: %v", err)
	}
	return
}

func (r *Postgres) CancelA2ABySysParam(ctx context.Context, transferId int, log string) (err error) {
	err = r.UpdateA2A(ctx,
		"partner_transfer",
		map[string]any{"status": 50, "updated_at": time.Now(), "log": log},
		map[string]any{"id": transferId},
	)
	if err != nil {
		err = fmt.Errorf("couldnt update partner transfer: %v", err)
		return
	}
	err = r.UpdateA2A(ctx,
		"partner_transaction",
		map[string]any{"status": 50, "transfer_id": nil, "log": log, "updated_at": time.Now()},
		map[string]any{"transfer_id": transferId},
	)
	if err != nil {
		err = fmt.Errorf("couldnt update partner transaction: %v", err)
		return
	}
	return
}

func (r *Postgres) JointUpdateA2A(ctx context.Context, transferId int, update map[string]any) {
	_ = r.UpdateA2A(ctx,
		"partner_transfer",
		update,
		map[string]any{"id": transferId},
	)

	_ = r.UpdateA2A(ctx,
		"partner_transaction",
		update,
		map[string]any{"transfer_id": transferId},
	)
}

func (r *Postgres) GetA2AToTransfer(ctx context.Context, partnerId, delayPeriod int) ([]model.A2ATransactionView, error) {
	var toDate = time.Now().AddDate(0, 0, -delayPeriod)
	return r.FetchA2A(ctx, map[string]any{"partner_id": partnerId, "to_date": toDate, "status": 30, "transfer_id": nil}, 0, 0)
}

func (r *Postgres) CancelA2ATransfer(ctx context.Context, transferId int, log string) error {
	return r.UpdateA2A(ctx, "partner_transfer",
		map[string]any{"status": 50, "log": log},
		map[string]any{"id": transferId},
	)
}

func (r *Postgres) ClearTransferWIthZeroAmount(ctx context.Context, transferId int) error {
	return r.UpdateA2A(ctx,
		"partner_transfer",
		map[string]any{"status": 50, "amount": 0, "log": "no amount to transfer"},
		map[string]any{"id": transferId},
	)
}

func (r *Postgres) UpdateTransferAmount(ctx context.Context, transferId int, amount int) error {
	return r.UpdateA2A(ctx,
		"partner_transfer",
		map[string]any{"amount": amount},
		map[string]any{"id": transferId},
	)
}

func (r *Postgres) UpdateIdpKeys(ctx context.Context, transferId int) error {
	newIdpKey := uuid.New().String()
	_, err := r.db.Exec(ctx, sqls.UpdateA2ATransferIdpKey, newIdpKey, transferId)
	if err != nil {
		return err
	}
	_, err = r.db.Exec(ctx, sqls.UpdateA2ATransactionsIdpKey, newIdpKey, transferId)
	return err
}
