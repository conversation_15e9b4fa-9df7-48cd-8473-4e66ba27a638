package postgres

import (
	"context"

	"billing_service/model"
	"billing_service/repo/postgres/sqls"

	null "github.com/guregu/null/v6"
	pgx "github.com/jackc/pgx/v5"
)

func (r *Postgres) XpanelGetClientVisaPayments(ctx context.Context, orderId int) (resp []model.PaymentXpanel, err error) {
	rows, err := r.db.Query(ctx, sqls.XpanelGetClientVisaPayments, orderId)
	if err != nil {
		return
	}
	defer rows.Close()

	for rows.Next() {
		var payment model.PaymentXpanel
		err = rows.Scan(&payment.Id, &payment.Amount, &payment.Invoice, &payment.Status, &payment.CreatedAt, &payment.UpdatedAt, &payment.CardId, &payment.CardNumber)
		if err != nil {
			return
		}
		resp = append(resp, payment)
	}

	err = rows.Err()

	return
}

func (r *Postgres) XpanelGetClientPaymePayments(ctx context.Context, orderId int) (resp model.XpanelClientPaymePaymentsResponse, err error) {
	rows, err := r.db.Query(ctx, sqls.XpanelGetClientPaymePayments, orderId)
	if err != nil {
		return
	}
	defer rows.Close()

	for rows.Next() {
		var (
			p model.XpanelPaymePayment
			c model.XpanelClientCard
		)
		err = rows.Scan(&p.Id, &p.OrderId, &p.Amount, &p.Status, &p.IsP2P, &p.Invoice, &p.CreatedAt, &c.Id, &c.Number, &c.Expire)
		if err != nil {
			return
		}
		if c.Id > 0 {
			p.Card = &c
		}
		if p.IsP2P {
			p.TypeId = 2
		} else {
			p.TypeId = 1
		}
		p.PaymentTypeId = 3 // TODO: payme payment type
		resp.Payments = append(resp.Payments, p)
	}

	err = rows.Err()

	return
}

func (r *Postgres) XpanelGetDriverA2CPayments(ctx context.Context, orderId int) (resp model.XpanelA2CPaymentsResponse, err error) {
	rows, err := r.db.Query(ctx, sqls.XpanelGetDriverA2CPayments, orderId)
	if err != nil {
		return
	}
	defer rows.Close()

	for rows.Next() {
		var p model.XpanelA2CPayment
		err = rows.Scan(&p.OrderId, &p.DriverId, &p.Amount, &p.Status, &p.ProviderId, &p.Invoice, &p.CreatedAt, &p.UpdatedAt, &p.CardNumber)
		if err != nil {
			return
		}
		switch p.Status {
		case 2:
			p.Status = 4
		case -1:
			p.Status = 50
		case 3:
			p.Status = 30
		case -2:
			p.Status = 21
		case 1:
			p.Status = 0
		}
		resp.Payments = append(resp.Payments, p)
	}

	resp.Count = len(resp.Payments)

	err = rows.Err()

	return
}

func (r *Postgres) XpanelGetOrderIsSuspicious(ctx context.Context, orderId int) (resp bool, err error) {
	err = r.db.QueryRow(ctx, sqls.XpanelGetOrderIsSuspicious, orderId).Scan(&resp)
	if err == pgx.ErrNoRows {
		err = nil
	}
	return
}

func (r *Postgres) XpanelUpdateClientDebtStatus(ctx context.Context, debtId int, status bool) (err error) {
	_, err = r.db.Exec(ctx, sqls.XpanelUpdateClientDebtStatus, debtId, status)
	return
}

func (r *Postgres) XpanelGetClientDebts(ctx context.Context, clientId, orderId, page, limit int, phone string) (resp model.XpanelClientDebtsResponse, err error) {
	err = r.db.QueryRow(ctx, sqls.XpanelGetClientDebtsCount, clientId, phone).Scan(&resp.Count)
	if err != nil {
		if err == pgx.ErrNoRows {
			err = nil
		}
		return
	}

	if resp.Count == 0 {
		return
	}

	var offset int

	if page > 1 {
		offset = (page - 1) * limit
	}

	rows, err := r.db.Query(ctx, sqls.XpanelGetClientDebts, clientId, orderId, phone, limit, offset)
	if err != nil {
		return
	}
	defer rows.Close()

	for rows.Next() {
		var (
			d      model.XpanelClientDebt
			isPaid bool
		)
		err = rows.Scan(&d.Id, &d.ClientId, &d.ClientPhone, &d.OrderId, &d.Amount, &d.CardHash, &d.CreatedAt, &d.PaidAt, &isPaid)
		if err != nil {
			return
		}
		if !isPaid {
			d.Status = 1
			d.PaidAt = ""
		} else {
			d.Status = 2
		}
		resp.Debts = append(resp.Debts, d)
	}

	err = rows.Err()

	return
}

func (r *Postgres) XpanelCreateClientDebt(ctx context.Context, orderId, clientId, amount int) (err error) {
	_, err = r.db.Exec(ctx, sqls.XpanelCreateDebt, orderId, clientId, "client", amount)
	return
}

func (r *Postgres) XpanelGetClientCards(ctx context.Context, clientId int, isDeleted bool, cardHash string, page, limit int) (resp model.XpanelClientCardsResponse, err error) {
	err = r.db.QueryRow(ctx, sqls.XpanelGetClientCardsCount, clientId, cardHash, isDeleted).Scan(&resp.Count)
	if err != nil {
		if err == pgx.ErrNoRows {
			err = nil
		}
		return
	}

	if resp.Count == 0 {
		return
	}

	var offset int

	if page > 1 {
		offset = (page - 1) * limit
	}

	rows, err := r.db.Query(ctx, sqls.XpanelGetClientCards, clientId, cardHash, isDeleted, limit, offset)
	if err != nil {
		return
	}
	defer rows.Close()

	for rows.Next() {
		var c model.XpanelClientCard2
		err = rows.Scan(&c.ClientID, &c.Number, &c.Expire, &c.PaymeCardHash, &c.IsDeleted)
		if err != nil {
			return
		}
		resp.Cards = append(resp.Cards, c)
	}

	err = rows.Err()

	return
}

func (r *Postgres) XpanelGetDriverCards(ctx context.Context, driverId int, isDeleted bool, page, limit int) (resp model.XpanelGetDriverCardsResponse, err error) {
	err = r.db.QueryRow(ctx, sqls.XpanelGetDriverCardsCount, driverId, isDeleted).Scan(&resp.Count)
	if err != nil {
		if err == pgx.ErrNoRows {
			err = nil
		}
		return
	}

	if resp.Count == 0 {
		return
	}

	var offset int

	if page > 1 {
		offset = (page - 1) * limit
	}

	rows, err := r.db.Query(ctx, sqls.XpanelGetDriverCards, driverId, isDeleted, limit, offset)
	if err != nil {
		return
	}
	defer rows.Close()

	for rows.Next() {
		var c model.XpanelDriverCard
		err = rows.Scan(&c.DriverID, &c.Number, &c.Expire, &c.CreatedAt, &c.DeletedAt, &c.IsDeleted)
		if err != nil {
			return
		}
		if !c.IsDeleted {
			c.DeletedAt = null.Time{}
		}
		resp.Cards = append(resp.Cards, c)
	}

	err = rows.Err()

	return
}
