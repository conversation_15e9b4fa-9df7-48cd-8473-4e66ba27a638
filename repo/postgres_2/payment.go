package postgres_2

import (
	"context"

	"billing_service/model"
	"billing_service/repo/postgres_2/sqls"
	// nolint
)

/*
const GetTransactions = `
SELECT
    t.id,
    t.account_number,
    t.dst_account_number,
    t.amount,
    t.op_type,
    o.alias,
    o.description,
    t.received_amount,
    t.order_id,
    t.type,
    t.description,
    t.month,
    t.created_at
FROM transactions t
JOIN operations o ON o.id = t.op_type
JOIN accounts a ON a.account_number = t.account_number
JOIN mytaxi_ids id ON id.id = a.id
JOIN mytaxi_users u ON u.mytaxi_id = id.mytaxi_id
WHERE true
AND (:search = '' OR t.description ILIKE :search || '%')
AND (:account_number = '' OR t.account_number = :account_number)
AND (:type = '' OR t.type = :type)
AND (:op_type = 0 OR t.op_type = :op_type)
AND (:order_id = '' OR t.order_id = :order_id)
AND (:user_id = 0 OR u.user_id = :user_id)
AND (:user_type = '' OR u.user_type = :user_type)
AND (:mytaxi_id = '' OR u.mytaxi_id = :mytaxi_id)
AND (:transaction_id = '' OR t.id = :transaction_id)
AND (:from_date = '' OR t.created_at >= :from_date)
AND (:to_date = '' OR t.created_at <= :to_date)
ORDER BY t.created_at DESC
LIMIT :limit OFFSET :offset;`

const GetTransactionsCount = `
SELECT count(*)
FROM transactions
JOIN accounts ON accounts.account_number =  transactions.account_number
JOIN mytaxi_ids ON mytaxi_ids.id = accounts.id
JOIN mytaxi_users ON mytaxi_users.mytaxi_id = mytaxi_ids.mytaxi_id
WHERE true
AND (:search = '' OR t.description ILIKE :search || '%')
AND (:account_number = '' OR t.account_number = :account_number)
AND (:type = '' OR t.type = :type)
AND (:op_type = 0 OR t.op_type = :op_type)
AND (:order_id = '' OR t.order_id = :order_id)
AND (:user_id = 0 OR u.user_id = :user_id)
AND (:user_type = '' OR u.user_type = :user_type)
AND (:mytaxi_id = '' OR u.mytaxi_id = :mytaxi_id)
AND (:transaction_id = '' OR t.id = :transaction_id)
AND (:from_date = '' OR t.created_at >= :from_date)
AND (:to_date = '' OR t.created_at <= :to_date)`

func (r *Postgres2) GetTransactionsHistory(ctx context.Context, req model.XpanelGetBillingPaymentsRequest) (resp model.TransactionsHistory, err error) {
	resp = model.TransactionsHistory{
		TransactionData: make([]model.Transaction, 0, req.Limit),
	}

	if req.Page > 0 {
		if req.Page == 1 {
			req.Page = 0
		} else {
			req.Page = (req.Page - 1) * req.Limit
		}
	}

	rows, err := r.db.NamedQueryContext(ctx, GetTransactions, req)
	if err != nil {
		return
	}
	for rows.Next() {
		transactionData := model.Transaction{}
		err = rows.Scan(
			&transactionData.ID,
			&transactionData.AccountNumber,
			&transactionData.DestinationAccountNumber,
			&transactionData.Amount,
			&transactionData.Operation.Type,
			&transactionData.Operation.Alias,
			&transactionData.Operation.Description,
			&transactionData.ReceivedAmount,
			&transactionData.OrderID,
			&transactionData.Type,
			&transactionData.Description,
			&transactionData.Month,
			&transactionData.CreatedAt,
		)
		if err != nil {
			return
		}

		resp.TransactionData = append(resp.TransactionData, transactionData)
	}

	row, err := r.db.NamedQueryContext(ctx, GetTransactionsCount, req)
	if err != nil {
		return
	}
	err = row.Scan(&resp.Count)

	return
}
*/

const (
	OpTypeDriverDeposit          int = 7
	OpTypePaymentGatewayDebit    int = 16
	OpTypePaymentGatewayWithdraw int = 17
	OpTypePartnerCredit          int = 18

	TrTypeDebit  = "debit"
	TrTypeCredit = "credit"
)

func (r *Postgres2) CreateBillingPayment(ctx context.Context, req model.WithdrawPaymentRequest) (err error) {
	_, err = r.db.Exec(ctx, sqls.CreateBillingPayment, req.AccountNumber, req.Amount, OpTypePaymentGatewayWithdraw, req.OrderID, TrTypeCredit)
	return
}
