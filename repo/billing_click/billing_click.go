package billing_click

import (
	"bytes"
	"context"
	"encoding/xml"
	"net/http"
	"time"
)

const (
	requestTimeout = 9 * time.Second
	errorText      = "Операция выполнена с ошибкой: "
)

type BillingClick struct {
	url        string
	bodyHeader string
	client     http.Client
}

func New(url, username, password string) *BillingClick {
	b := &BillingClick{
		url:        url,
		bodyHeader: "<ROOT><username>" + username + "</username><password>" + password + "</password><action>",
		client:     http.Client{Timeout: requestTimeout},
	}

	return b
}

type ROOT struct {
	SuccessCode int     `xml:"success"`
	ErrorCode   int     `xml:"error"`
	ErrorNote   string  `xml:"error_note"`
	Balance     float64 `xml:"balance"`
}

func (r *BillingClick) sendRequest(ctx context.Context, req []byte, resp any) (err error) {
	httpReq, err := http.NewRequestWithContext(ctx, "POST", r.url, bytes.NewReader(req))
	if err != nil {
		return
	}

	httpReq.Header = http.Header{
		"Content-Type": {"application/xml"},
	}

	httpResp, err := r.client.Do(httpReq)
	if err != nil {
		return
	}
	defer httpResp.Body.Close()

	err = xml.NewDecoder(httpResp.Body).Decode(&resp)

	return
}
