package billing_click

import (
	"billing_service/model"
	"context"
	"errors"
	"fmt"
	"time"
)

func (r *BillingClick) GetPartnerBalance(ctx context.Context, parkId int) (resp int, err error) {
	const body = "get_partner_balance</action><partner_id>%d</partner_id></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, parkId)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	resp = int(result.Balance)

	return
}

func (r *BillingClick) GetPartner(ctx context.Context, partnerId int) (resp model.PartnerResponse, err error) {
	const body = "get_partner</action><partner_id>%d</partner_id></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, partnerId)
	var result struct {
		ROOT
		Name    string  `xml:"name"`
		INN     int     `xml:"inn"`
		Percent float64 `xml:"prcnt"`
		Status  int     `xml:"status"`
	}

	err = r.sendRequest(ctx, []byte(req), &result)

	resp = model.PartnerResponse{
		ID:      partnerId,
		Name:    result.Name,
		INN:     result.INN,
		Percent: result.Percent,
		Status:  result.Status,
	}

	return
}

func (r *BillingClick) UpdatePartner(ctx context.Context, partnerId int, name, inn string, percent float64) (err error) {
	const body = "partner_edit</action><partner_id>%d</partner_id><name>%s</name><inn>%s</inn><prcnt>%f</prcnt></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, partnerId, name, inn, percent)
	var result ROOT
	err = r.sendRequest(ctx, []byte(req), &result)

	if result.ErrorCode != 0 {
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	return
}

func (r *BillingClick) CreatePartner(ctx context.Context, partnerId int, name, inn string, percent float64) (err error) {
	const body = "partner_add</action><partner_id>%d</partner_id><name>%s</name><inn>%s</inn><prcnt>%f</prcnt></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, partnerId, name, inn, percent)
	var result ROOT
	err = r.sendRequest(ctx, []byte(req), &result)

	if result.ErrorCode != 0 {
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	return
}

func (r *BillingClick) DepositPartner(ctx context.Context, partnerId int, amount float64, description, date, payment string) (err error) {
	const body = "partner_deposit</action><mytaxi_id>%d</mytaxi_id><partner_id>%d</partner_id><amount>%f</amount><description>%s</description><dtime></dtime><payment>%s</payment><cash>1</cash></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, time.Now().UnixMilli(), partnerId, amount, description, payment)
	var result ROOT
	err = r.sendRequest(ctx, []byte(req), &result)

	if result.ErrorCode != 0 {
		switch result.ErrorNote {
		case "Транзакция уже существует":
			return r.DepositPartner(ctx, partnerId, amount, description, date, payment)
		}
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	return
}

func (r *BillingClick) CreatePark(ctx context.Context, parkId int, name, regDate, phone, inn, account, partnerId string, percent float64) (err error) {
	const body = "company_reg</action><company_id>%d</company_id><name>%s</name><date_create_company>%s</date_create_company><inn>%s</inn><acc>%s</acc><phone>%s</phone><partner_id>%s</partner_id><prcnt>%f</prcnt></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, parkId, name, regDate, inn, account, phone, partnerId, percent)
	var result ROOT
	err = r.sendRequest(ctx, []byte(req), &result)

	if result.ErrorCode != 0 {
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	return
}

func (r *BillingClick) UpdatePark(ctx context.Context, parkId int, name, phone, regDate, inn, account, partnerId string, percent float64) (err error) {
	const body = "company_edit</action><company_id>%d</company_id><name>%s</name><date_create_company>%s</date_create_company><inn>%s</inn><acc>%s</acc><phone>%s</phone><partner_id>%s</partner_id><prcnt>%f</prcnt></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, parkId, name, regDate, inn, account, phone, partnerId, percent)
	var result ROOT
	err = r.sendRequest(ctx, []byte(req), &result)

	if result.ErrorCode != 0 {
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	return
}

func (r *BillingClick) PartnerToPark(ctx context.Context, partnerId, taxiParkId, amount int) (err error) {
	const body = "partner_to_taxipark</action><mytaxi_id>%d</mytaxi_id><partner_id>%d</partner_id><taxipark_id>%d</taxipark_id><amount>%d</amount></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, time.Now().UnixMilli(), partnerId, taxiParkId, amount)
	var result ROOT
	err = r.sendRequest(ctx, []byte(req), &result)

	if result.ErrorCode != 0 {
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	return
}
