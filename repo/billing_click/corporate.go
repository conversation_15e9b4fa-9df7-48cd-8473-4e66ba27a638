package billing_click

import (
	"context"
	"errors"
	"fmt"
	"time"

	"billing_service/model"
)

func (r *BillingClick) GetCorpBalance(ctx context.Context, corpId int) (resp model.CorpBalanceResponse, err error) {
	req := r.bodyHeader + fmt.Sprintf("get_acc_bal_corp_client</action><corp_client_id>%d</corp_client_id></ROOT>", corpId)
	var result struct {
		ROOT
		Balance float64 `xml:"bal"`
	}

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true
	resp.Balance = int(result.Balance)

	return
}

func (r *BillingClick) CreateCorp(ctx context.Context, corpId int, name, regDate, phone, account, inn string) (resp model.Response, err error) {
	const body = "corp_client_reg</action><corp_client_id>%d</corp_client_id><name>%s</name><date_create_corp>%s</date_create_corp><phone>%s</phone><acc>%s</acc><inn>%s</inn></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, corpId, name, regDate, phone, account, inn)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) UpdateCorp(ctx context.Context, corpId int, name, regDate, phone, account, inn string) (resp model.Response, err error) {
	const body = "corp_client_edit</action><corp_client_id>%d</corp_client_id><name>%s</name><date_create_corp>%s</date_create_corp><phone>%s</phone><acc>%s</acc><inn>%s</inn></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, corpId, name, regDate, phone, account, inn)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) CorpDeposit(ctx context.Context, corpId, amount, amountType int, description string) (err error) {
	const body = "corp_deposit</action><mytaxi_id>%d</mytaxi_id><corp_id>%d</corp_id><amount>%d</amount><cash>%d</cash>><description>%s</description></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, time.Now().UnixMilli(), corpId, amount, amountType, description)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)

	if result.ErrorCode != 0 {
		switch result.ErrorNote {
		case "Транзакция уже существует":
			return r.CorpDeposit(ctx, corpId, amount, amountType, description)
		}
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	if result.ErrorCode != 0 {
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	return
}

func (r *BillingClick) GetOperations(ctx context.Context, operationName string) (resp model.OperationsResponse, err error) {
	const body = "get_accounts_for_operation</action><operation>%s</operation></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, operationName)

	type xmlAccount struct {
		ID   int    `xml:"id"`
		Name string `xml:"name"`
	}

	type xmlOperationsResponse struct {
		Success   int          `xml:"success"`
		Error     int          `xml:"error"`
		ErrorNote string       `xml:"error_note"`
		Accounts  []xmlAccount `xml:"account"`
	}

	var result xmlOperationsResponse

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return resp, fmt.Errorf("failed to send request: %w", err)
	}

	if result.Error != 0 {
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	resp.Success = true
	resp.Accounts = make([]model.Account, len(result.Accounts))

	for i, acc := range result.Accounts {
		resp.Accounts[i] = model.Account{
			ID:   acc.ID,
			Name: acc.Name,
		}
	}

	return
}

func (r *BillingClick) GetTaxiParkBalance(ctx context.Context, parkId int) (resp int, err error) {
	const body = "get_acc_bal_company</action><company_id>%d</company_id></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, parkId)
	var result struct {
		ROOT
		Balance float64 `xml:"bal"`
	}

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	resp = int(result.Balance)

	return
}
