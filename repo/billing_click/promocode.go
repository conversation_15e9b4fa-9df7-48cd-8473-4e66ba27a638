package billing_click

import (
	"context"
	"errors"
	"fmt"

	"billing_service/model"
)

func (r *BillingClick) GetPromocodeInfo(ctx context.Context, clientId int, promocode string) (resp model.PromocodeInfo, err error) {
	req := r.bodyHeader + fmt.Sprintf("get_promocode_info</action><client_id>%d</client_id><promocode>%s</promocode></ROOT>", clientId, promocode)
	var result struct {
		ROOT
		model.PromocodeInfo
	}

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	result.TotalUsage = float64(int(result.TotalUsage))
	resp = result.PromocodeInfo
	resp.Success = true

	return
}

func (r *BillingClick) GetPromocodeValidation(ctx context.Context, clientId int, promocode string) (errType string, err error) {
	req := r.bodyHeader + fmt.Sprintf("validate_promocode</action><client_id>%d</client_id><promocode>%s</promocode></ROOT>", clientId, promocode)
	var result struct {
		ROOT
		PromoStatus int `xml:"promo_status"`
	}

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	if result.PromoStatus != 0 {
		errType = model.PromoStasuses[result.PromoStatus]
		if errType == "" {
			errType = "promocode_is_not_active"
		}
		err = errors.New("promo not valid")
		return
	}

	return
}
