package billing_click

import (
	"context"
	"errors"

	"billing_service/model"
)

func (r *BillingClick) XpanelGetAllBalance(ctx context.Context) (resp model.AllBalanceResponse, err error) {
	req := r.<PERSON><PERSON>eader + "get_all_balance</action></ROOT>"
	var result struct {
		ROOT
		ClientsCashbackBalance float64 `xml:"clients_bal"`
		DriversBalance         float64 `xml:"drivers_bal"`
	}

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	resp.ClientsCashbackBalance = int(result.ClientsCashbackBalance)
	resp.DriversBalance = int(result.DriversBalance)

	return
}
