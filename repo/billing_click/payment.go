package billing_click

import (
	"context"
	"errors"
	"fmt"
	"time"

	"billing_service/model"
)

const orderInfoXml = `<point_a>%s</point_a>
<point_b>%s</point_b>
<coordinates_a_x>%f</coordinates_a_x>
<coordinates_a_y>%f</coordinates_a_y>
<coordinates_b_x>%f</coordinates_b_x>
<coordinates_b_y>%f</coordinates_b_y>
<time_order_start>%s</time_order_start>
<time_driver_arrived>%s</time_driver_arrived>
<time_client_sat>%s</time_client_sat>
<time_order_finished>%s</time_order_finished>
<distance_to_client>%d</distance_to_client>
<distance_before_client_sat>%d</distance_before_client_sat>
<order_distance>%d</order_distance>
<government_num_car>%s</government_num_car>
<model_car>%s</model_car>
<color_car>%s</color_car>
<tariff_name>%s</tariff_name>
<min_sum_by_tarif>%d</min_sum_by_tarif>
<cost_waiting_time>%d</cost_waiting_time>
<cost_km>%d</cost_km>
<include_km_in_tarif>%d</include_km_in_tarif>
<include_waiting_time>%d</include_waiting_time>
<rate>%.1f</rate>
<waiting_time_order>%d</waiting_time_order>
<promocode>%s</promocode>
<paid_from_bonus>%d</paid_from_bonus>
<without_cashback>%d</without_cashback>
</ROOT>`

func convertOrderInfoToXml(order model.OrderInfo) string {
	withoutCashback := 0
	if order.PaidFromBonus.Int64 > 0 {
		withoutCashback = 1
	}
	return fmt.Sprintf(orderInfoXml,
		order.FromAdrs,
		order.ToAdrs,
		order.CoordinatesAX,
		order.CoordinatesAY,
		order.CoordinatesBX,
		order.CoordinatesBY,
		order.Date,
		order.DrArrivedTime.String,
		order.ClientSatTime.String,
		order.EndTime.String,
		order.DistanceToClient.Int64,
		order.DistanceBeforeSat.Int64,
		order.DrivingDistance.Int64,
		order.CarNumber.String,
		order.CarName.String,
		order.CarColorNameRu.String,
		order.TarifName,
		order.Minimalka.Int64,
		order.OjidaniyMinut.Int64,
		order.Vgorode.Int64,
		order.VkMin.Int64,
		order.VkOjidaniy.Int64,
		order.InterestRate.Float64,
		order.WaitingTime.Int64,
		order.Promocode.String,
		order.PaidFromBonus.Int64,
		withoutCashback,
	)
}

func (r *BillingClick) BillingPayOrderCash(ctx context.Context, req model.PayOrderRequest, orderInfo model.OrderInfo) (balance int, err error) {
	const body = "client_pay_cash</action><mytaxi_id>%d</mytaxi_id><ride_id>%d</ride_id><client_id>%d</client_id><driver_id>%d</driver_id><order_initiator>%d</order_initiator><amount>%d</amount>"
	reqBody := r.bodyHeader + fmt.Sprintf(body, time.Now().UnixMilli(), req.OrderId, req.ClientId, req.DriverId, req.InitiatorId, req.TotalCost) + convertOrderInfoToXml(orderInfo)
	var result ROOT

	err = r.sendRequest(ctx, []byte(reqBody), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		switch result.ErrorNote {
		case "Транзакция уже существует":
			return r.BillingPayOrderCash(ctx, req, orderInfo) // TODO recursive for transaction id
		case "Поездка с таким ID уже существует":
			return
		}
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	if result.SuccessCode != 1 {
		err = fmt.Errorf("billing click response not success: %v", result)
		return
	}

	balance = int(result.Balance)

	return
}

func (r *BillingClick) BillingPayOrderCashWithPromo(ctx context.Context, req model.PayOrderRequest, orderInfo model.OrderInfo) (balance, promoAmount, promoErrorCode int, err error) {
	const body = "client_pay_cash</action><mytaxi_id>%d</mytaxi_id><ride_id>%d</ride_id><client_id>%d</client_id><driver_id>%d</driver_id><order_initiator>%d</order_initiator><amount>%d</amount>"
	reqBody := r.bodyHeader + fmt.Sprintf(body, time.Now().UnixMilli(), req.OrderId, req.ClientId, req.DriverId, req.InitiatorId, req.TotalCost) + convertOrderInfoToXml(orderInfo)
	var result struct {
		ROOT
		PromoAmount    float32 `xml:"amount_promo"`
		PromoErrorCode int     `xml:"promo_error_code"`
	}

	err = r.sendRequest(ctx, []byte(reqBody), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		switch result.ErrorNote {
		case "Транзакция уже существует":
			return r.BillingPayOrderCashWithPromo(ctx, req, orderInfo) // TODO recursive for transaction id
		case "Поездка с таким ID уже существует":
			return
		}
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	if result.SuccessCode != 1 {
		err = fmt.Errorf("billing click response not success: %v", result)
		return
	}

	promoAmount = int(result.PromoAmount)
	promoErrorCode = result.PromoErrorCode
	balance = int(result.Balance)

	return
}

func (r *BillingClick) BillingPayOrderCorp(ctx context.Context, req model.PayOrderRequest, orderInfo model.OrderInfo) (balance int, err error) {
	const body = "corp_pay_to_card</action><mytaxi_id>%d</mytaxi_id><ride_id>%d</ride_id><client_id>%d</client_id><corp_id>%d</corp_id><driver_id>%d</driver_id><order_initiator>%d</order_initiator><amount>%d</amount>"
	reqBody := r.bodyHeader + fmt.Sprintf(body, time.Now().UnixMilli(), req.OrderId, req.ClientId, req.CorpId, req.DriverId, req.InitiatorId, req.TotalCost) + convertOrderInfoToXml(orderInfo)
	var result ROOT

	err = r.sendRequest(ctx, []byte(reqBody), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		switch result.ErrorNote {
		case "Транзакция уже существует":
			return r.BillingPayOrderCorp(ctx, req, orderInfo) // TODO recursive for transaction id
		case "Поездка с таким ID уже существует":
			return
		}
		err = errors.New(errorText + result.ErrorNote)
		return
	}

	if result.SuccessCode != 1 {
		err = fmt.Errorf("billing click response not success: %v", result)
		return
	}

	balance = int(result.Balance)

	return
}
