package billing_click

import (
	"context"
	"fmt"

	"billing_service/model"
)

func (r *BillingClick) CreateClient(ctx context.Context, clientId int, phone, firstName, lastName string) (resp model.Response, err error) {
	const body = "client_reg</action><client_id>%d</client_id><phone>%s</phone><first_name>%s</first_name><last_name>%s</last_name></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, clientId, phone, firstName, lastName)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) UpdateClient(ctx context.Context, clientId int, phone, firstName, lastName string) (resp model.Response, err error) {
	const body = "client_edit</action><client_id>%d</client_id><phone>%s</phone><first_name>%s</first_name><last_name>%s</last_name></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, clientId, phone, firstName, lastName)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) DeleteClient(ctx context.Context, clientId int) (resp model.Response, err error) {
	const body = "client_fire</action><client_id>%d</client_id></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, clientId)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) UpdateClientCashbackMode(ctx context.Context, clientId, mode int) (resp model.Response, err error) {
	req := r.bodyHeader + fmt.Sprintf("client_bonus_on_off</action><client_id>%d</client_id><status>%d</status></ROOT>", clientId, mode)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) GetClientCashback(ctx context.Context, clientId int) (resp model.CorpClientBalanceResponse, err error) {
	req := r.bodyHeader + fmt.Sprintf("get_client_bonus</action><client_id>%d</client_id></ROOT>", clientId)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		if result.ErrorCode == -205 {
			resp.Success = true
			return
		}
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true
	resp.Balance = int(result.Balance)
	resp.Available = true

	return
}
