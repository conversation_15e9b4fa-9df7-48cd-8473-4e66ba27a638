package billing_click

import (
	"context"
	"fmt"

	"billing_service/model"

	null "github.com/guregu/null/v6"
)

func (r *BillingClick) CreateCorpClient(ctx context.Context, clientId, corpId int, phone, firstName, lastName string) (resp model.Response, err error) {
	const body = "client_reg</action><client_id>%d</client_id><phone>%s</phone><first_name>%s</first_name><last_name>%s</last_name><corp><corp_id>%d</corp_id></corp></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, clientId, phone, firstName, lastName, corpId)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) UpdateCorpClient(ctx context.Context, clientId, corpId int, phone, firstName, lastName string) (resp model.Response, err error) {
	const body = "client_edit</action><client_id>%d</client_id><phone>%s</phone><first_name>%s</first_name><last_name>%s</last_name><corp><corp_id>%d</corp_id></corp></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, clientId, phone, firstName, lastName, corpId)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) GetCorpClientBalance(ctx context.Context, clientId, corpId int) (resp model.CorpClientBalanceResponse, err error) {
	req := r.bodyHeader + fmt.Sprintf("ride_possibility</action><client_id>%d</client_id><corp_client_id>%d</corp_client_id></ROOT>", clientId, corpId)
	var result struct {
		ROOT
		Available    bool     `xml:"ride_possibility"`
		BalanceLimit float64  `xml:"bal_limit"`
		ErrorStatus  null.Int `xml:"error_status"`
	}

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true
	resp.Balance = int(result.BalanceLimit)
	resp.Available = result.Available
	resp.ErrorStatus = int(result.ErrorStatus.Int64)

	return
}

func (r *BillingClick) GetCorpClientLimit(ctx context.Context, clientId, corpId int) (resp model.CorpClientLimitResponse, err error) {
	req := r.bodyHeader + fmt.Sprintf("limit_get</action><client_id>%d</client_id><corp_id>%d</corp_id></ROOT>", clientId, corpId)
	var result struct {
		ROOT
		Amount        float64 `xml:"amount"`
		Repeatability string  `xml:"repeatability"`
		IsExpireDate  bool    `xml:"expire_date"`
	}

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true
	resp.Amount = int(result.Amount)
	resp.Repeatability = result.Repeatability
	resp.IsExpireDate = result.IsExpireDate

	return
}

func (r *BillingClick) CreateCorpClientLimit(ctx context.Context, clientId, corpId, amount int, repeat string) (resp model.Response, err error) {
	const body = "limit_insert</action><client_id>%d</client_id><corp_id>%d</corp_id><amount>%d</amount><repeatability>%s</repeatability></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, clientId, corpId, amount, repeat)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) UpdateCorpClientLimit(ctx context.Context, clientId, corpId, amount int, repeat string) (resp model.Response, err error) {
	const body = "limit_update</action><client_id>%d</client_id><corp_id>%d</corp_id><amount>%d</amount><repeatability>%s</repeatability></ROOT>"
	req := r.bodyHeader + fmt.Sprintf(body, clientId, corpId, amount, repeat)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}

func (r *BillingClick) DeleteCorpClientLimit(ctx context.Context, clientId, corpId int) (resp model.Response, err error) {
	req := r.bodyHeader + fmt.Sprintf("limit_delete</action><client_id>%d</client_id><corp_id>%d</corp_id></ROOT>", clientId, corpId)
	var result ROOT

	err = r.sendRequest(ctx, []byte(req), &result)
	if err != nil {
		return
	}

	if result.ErrorCode != 0 {
		resp.Message = errorText + result.ErrorNote
		return
	}

	resp.Success = true

	return
}
