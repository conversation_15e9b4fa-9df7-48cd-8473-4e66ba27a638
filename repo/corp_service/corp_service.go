package corp_service

import (
	"context"
	"fmt"

	"billing_service/model"
	"billing_service/util/http"
)

type CorpService struct {
	host   string
	client *http.Client
}

func New(host string) *CorpService {
	b := &CorpService{
		host:   host,
		client: http.New(),
	}
	return b
}

func (s *CorpService) GetCorpClientBalanceInfo(ctx context.Context, clientId int) (resp model.CorpClientBalance, err error) {
	url := s.host + fmt.Sprintf("/v1/clients/%d/corp-client-balance", clientId)

	_, err = s.client.RequestWithoutBody(ctx, "GET", url, &resp)
	if err != nil {
		err = fmt.Errorf("get corp client %d balance: %v", clientId, err)
		return
	}

	return
}
