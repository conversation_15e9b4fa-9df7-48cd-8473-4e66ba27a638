package api

import (
	"context"
	"fmt"
	"sync"
	"time"

	"billing_service/app"
	"billing_service/repo"
	"billing_service/util/config"
	"billing_service/util/logger"
	"billing_service/util/nats"

	"github.com/gofiber/fiber/v3"
)

var (
	once sync.Once
)

type handler struct {
	ctx  context.Context
	cfg  *config.Config
	log  *logger.Logger
	app  *app.App
	repo *repo.Repo
	nats *nats.Client
}

func Run(ctx context.Context) {
	once.Do(func() {
		cfg := config.Get()
		log := logger.Get(cfg.LogLevel)

		h := &handler{
			ctx:  ctx,
			cfg:  cfg,
			log:  log,
			app:  app.Get(ctx),
			repo: repo.Get(ctx, cfg, log),
			nats: nats.Get(cfg.Nats.Host, cfg.Nats.Token),
		}

		h.subscribeToEvents()

		api := h.new()

		log.Info("running http server at: ", cfg.Host)

		if err := api.Listen(cfg.Host, fiber.ListenConfig{GracefulContext: ctx}); err != nil {
			panic(fmt.Errorf("cannot run http server: %v", err))
		}

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		h.app.Shutdown(ctx)

		h.log.Info("shutting down, server exiting")
	})
}
