package api

import (
	"strconv"

	"billing_service/util"

	fiber "github.com/gofiber/fiber/v3"
)

func (h *handler) CreateCorpClient(c fiber.Ctx) error {
	clientId := util.ParseInt(c.<PERSON>ms("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	corpId := util.ParseInt(c.<PERSON>ms("corp_id"))
	if corpId == 0 {
		return h.badRequestResponse(c, "incorrect corp_id")
	}

	resp, err := h.repo.CreateCorpClient(c.Context(), clientId, corpId, c.<PERSON>("phone"), c.<PERSON>("first_name"), c.<PERSON>("last_name"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) UpdateCorpClient(c fiber.Ctx) error {
	clientId := util.ParseInt(c.<PERSON>("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	corpId, err := strconv.Atoi(c.Params("corp_id"))
	if err != nil {
		return h.badRequestResponse(c, "incorrect corp_id")
	}

	resp, err := h.repo.UpdateCorpClient(c.Context(), clientId, corpId, c.Params("phone"), c.Params("first_name"), c.Params("last_name"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) GetCorpClientBalance(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	corpId := util.ParseInt(c.Params("corp_id"))
	if corpId == 0 {
		return h.badRequestResponse(c, "incorrect corp_id")
	}

	resp, err := h.repo.GetCorpClientBalance(c.Context(), clientId, corpId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

// type corpClientLimitRequest struct {
// 	ClientId int `uri:"client_id"`
// 	CorpId   int `uri:"corp_id"`
// 	Amount   int `uri:"amount"`
// 	Repeat   string `uri:"repeat"`
// }
// var req corpClientLimitRequest
// err := c.Bind().URI(&req)
// if err != nil {
// 	return h.badRequestResponse(c, "incorrect params")
// }

func (h *handler) GetCorpClientLimit(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	corpId := util.ParseInt(c.Params("corp_id"))
	if corpId == 0 {
		return h.badRequestResponse(c, "incorrect corp_id")
	}

	resp, err := h.repo.GetCorpClientLimit(c.Context(), clientId, corpId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) CreateCorpClientLimit(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	corpId := util.ParseInt(c.Params("corp_id"))
	if corpId == 0 {
		return h.badRequestResponse(c, "incorrect corp_id")
	}

	amount := util.ParseInt(c.Params("amount"))
	if corpId == 0 {
		return h.badRequestResponse(c, "incorrect amount")
	}

	resp, err := h.repo.CreateCorpClientLimit(c.Context(), clientId, corpId, amount, c.Params("repeat"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) UpdateCorpClientLimit(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	corpId := util.ParseInt(c.Params("corp_id"))
	if corpId == 0 {
		return h.badRequestResponse(c, "incorrect corp_id")
	}

	amount := util.ParseInt(c.Params("amount"))
	if corpId == 0 {
		return h.badRequestResponse(c, "incorrect amount")
	}

	resp, err := h.repo.UpdateCorpClientLimit(c.Context(), clientId, corpId, amount, c.Params("repeat"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) DeleteCorpClientLimit(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	corpId := util.ParseInt(c.Params("corp_id"))
	if corpId == 0 {
		return h.badRequestResponse(c, "incorrect corp_id")
	}

	resp, err := h.repo.DeleteCorpClientLimit(c.Context(), clientId, corpId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}
