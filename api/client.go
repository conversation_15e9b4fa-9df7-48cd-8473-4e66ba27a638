package api

import (
	"context"
	"net/http"

	"billing_service/util"

	fiber "github.com/gofiber/fiber/v3"
)

const (
	ClientType      = "client"
	CorpType        = "corp"
	CorpCompanyType = "corporate-company"
)

func (h *handler) CreateClient(c fiber.Ctx) error {
	clientId := util.ParseInt(c.<PERSON>ms("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	resp, err := h.repo.CreateClient(c.Context(), clientId, c.Params("phone"), c.<PERSON>("first_name"), c.<PERSON>("last_name"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) UpdateClient(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	resp, err := h.repo.UpdateClient(c.Context(), clientId, c.<PERSON>("phone"), c.<PERSON>("first_name"), c.<PERSON>ms("last_name"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) DeleteClient(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	resp, err := h.repo.DeleteClient(c.Context(), clientId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) UpdateClientCashbackMode(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	mode := util.ParseInt(c.Params("mode"))

	resp, err := h.repo.UpdateClientCashbackMode(c.Context(), clientId, mode)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) GetClientPaymentTypes(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	resp, err := h.app.GetClientPaymentTypes(c.Context(), clientId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) GetClientPaymentTypesOld(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	resp, err := h.app.GetClientPaymentTypesOLd(c.Context(), clientId, util.GetAppLanguage(c))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) GetClientPaymeCards(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	resp, err := h.app.GetClientPaymeCards(c.Context(), clientId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(map[string]any{"status": "success", "cards": resp})
}

func (h *handler) GetCardInfo(c fiber.Ctx) error {
	cardId := util.ParseInt(c.Params("card_id"))
	if cardId == 0 {
		return h.badRequestResponse(c, "incorrect card_id")
	}

	resp, err := h.repo.GetCard(c.Context(), cardId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

type AddClientPaymeCardRequest struct {
	Token string `json:"token"`
}

func (h *handler) AddClientPaymeCard(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	var req AddClientPaymeCardRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	errType, err := h.app.AddClientPaymeCard(c.Context(), clientId, req.Token)
	if err != nil {
		if errType != "" {
			return h.errorResponse(c, errType, err.Error())
		} else {
			return h.serviceErrorResponse(c, err.Error())
		}
	}

	return c.JSON(map[string]string{"status": "success"})
}

type deleteClientPaymeCardRequest struct {
	CardId   int    `json:"card_id"`
	CardType string `json:"card_type"`
}

func (h *handler) DeleteClientPaymeCard(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	var req deleteClientPaymeCardRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	if req.CardType == "" {
		return h.badRequestResponse(c, "empty card_type")
	}

	if req.CardType == "payme" {
		errType, err := h.app.DeleteClientPaymeCard(c.Context(), clientId, req.CardId)
		if err != nil {
			if errType != "" {
				return h.errorResponse(c, errType, err.Error())
			} else {
				return h.serviceErrorResponse(c, err.Error())
			}
		}
	}

	return c.JSON(map[string]string{"status": "success"})
}

func (h *handler) GetClientDebts(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	resp, err := h.repo.GetDebts(c.Context(), clientId, "client")
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(map[string]any{"status": "success", "debts": resp})
}

func (h *handler) GetClientCardSolvency(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	cardId := util.ParseInt(c.Params("card_id"))
	if cardId == 0 {
		return h.badRequestResponse(c, "incorrect card_id")
	}

	amount := util.ParseInt(c.Query("amount"))
	if amount == 0 {
		return h.badRequestResponse(c, "incorrect amount")
	}

	errType, err := h.app.GetClientCardSolvency(c.Context(), clientId, cardId, amount)
	if err != nil {
		if errType != "" {
			return h.errorResponse(c, errType, err.Error())
		}
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.SendStatus(http.StatusOK)
}

func (h *handler) GetOrderCardBalance(c fiber.Ctx) error {
	orderId := util.ParseInt(c.Params("order_id"))
	if orderId == 0 {
		return h.badRequestResponse(c, "incorrect order_id")
	}

	amount := util.ParseInt(c.Query("amount"))
	if amount == 0 {
		return h.badRequestResponse(c, "incorrect amount")
	}

	resp, err := h.app.GetOrderCardBalance(c.Context(), orderId, amount)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) AddAtmosCard(c fiber.Ctx) error {
	userType := c.Query("user_type")
	providerType := c.Query("provider_type")
	userId := util.ParseInt(c.Query("user_id"))
	if userId == 0 {
		return h.badRequestResponse(c, "incorrect user_id")
	}

	if userType == CorpCompanyType {
		userType = CorpType
	} else if userType != ClientType && userType != CorpType {
		return h.badRequestResponse(c, "incorrect user_type")
	}

	switch providerType {
	case "atmos":
		if !h.repo.GetSysParam("is_atmos_enabled", "0").Bool() {
			return h.errorResponse(c, "payment_provider_unavailable", "Atmos payment provider is disabled")
		}
		resp, err := h.app.AddAtmosCard(c.Context(), userId, userType)
		if err != nil {
			return h.serviceErrorResponse(c, err.Error())
		}
		return c.JSON(resp)
	default:
		return h.badRequestResponse(c, "unsupported provider_type")
	}
}

func (h *handler) GetAtmosCards(c fiber.Ctx) error {
	userId := util.ParseInt(c.Params("user_id"))
	userType := c.Query("user_type")

	if userId == 0 {
		return h.badRequestResponse(c, "incorrect user_id")
	}

	if userType == CorpCompanyType {
		userType = CorpType
	} else if userType != ClientType && userType != CorpType {
		return h.badRequestResponse(c, "incorrect user_type")
	}

	resp, err := h.app.GetAtmosCards(context.Background(), userId, userType)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.Map{
		"result": resp,
		"status": "success",
	})
}

type deleteCardRequest struct {
	UserType string `json:"user_type"`
	CardID   int    `json:"card_id"`
}

func (h *handler) DeleteCard(c fiber.Ctx) error {
	userId := util.ParseInt(c.Params("user_id"))
	if userId <= 0 {
		return h.badRequestResponse(c, "incorrect user_id")
	}

	var req deleteCardRequest
	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	if req.CardID == 0 {
		return h.badRequestResponse(c, "incorrect card_id")
	}

	if req.UserType != ClientType {
		return h.badRequestResponse(c, "incorrect user_type")
	}

	errType, err := h.app.DeleteCard(context.Background(), req.CardID, userId, req.UserType)
	if err != nil {
		if errType != "" {
			return h.errorResponse(c, errType, err.Error())
		} else {
			return h.serviceErrorResponse(c, err.Error())
		}
	}

	return c.JSON(fiber.Map{
		"status": "success",
	})
}

func (h *handler) GetClientCashback(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	resp, err := h.app.GetClientCashback(c.Context(), clientId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) GetClientCashbackOld(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	resp, err := h.app.GetClientCashbackOld(c.Context(), clientId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) GetClickSuperAppPaymentLink(c fiber.Ctx) error {
	order_id := util.ParseInt(c.Query("order_id"))
	if order_id == 0 {
		return h.badRequestResponse(c, "incorrect order_id")
	}

	link, err := h.app.GetClickSuperAppPaymentLink(c.Context(), order_id)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.Map{"payment_link": link})
}
