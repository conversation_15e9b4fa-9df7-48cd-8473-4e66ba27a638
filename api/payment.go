package api

import (
	"billing_service/model"
	"billing_service/util"

	fiber "github.com/gofiber/fiber/v3"
)

func (h *handler) PayDebt(c fiber.Ctx) error {
	clientId := util.ParseInt(c.<PERSON>ms("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	var req model.PayDebtRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	if req.CardIdRaw.Value == 0 {
		return h.badRequestResponse(c, "incorrect card_id")
	}

	errType, err := h.app.PayDebt(c.Context(), clientId, req.CardIdRaw.Value, "client", req.CardType)
	if err != nil {
		if errType != "" {
			return h.errorResponse(c, errType, err.Error())
		} else {
			return h.serviceErrorResponse(c, err.Error())
		}
	}

	return c.JSO<PERSON>(map[string]string{"status": "success"})
}

func (h *handler) PayTips(c fiber.Ctx) error {
	orderId := util.ParseInt(c.Params("order_id"))
	if orderId == 0 {
		return h.badRequestResponse(c, "incorrect order_id")
	}

	var req model.PayTipsRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	if req.Amount == 0 || req.ClientId == 0 || req.CardId == 0 {
		return h.badRequestResponse(c, "incorrect request body")
	}

	req.OrderId = orderId

	err = h.app.PayTips(c.Context(), req)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(map[string]string{"status": "success"})
}
