package api

import (
	"fmt"

	"billing_service/model"
	"billing_service/util"

	"github.com/gofiber/fiber/v3"
)

func (h *handler) GetA2AForOrder(c fiber.Ctx) error {
	orderId := util.ParseInt(c.Params("order_id"))
	if orderId == 0 {
		return h.badRequestResponse(c, "incorrect order_id")
	}

	resp, err := h.app.GetA2ATransactionForOrder(c.Context(), orderId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	if len(resp) == 0 {
		return h.errorResponse(c, "not_found", fmt.Sprintf("Partner transaction not found for order_id: %d", orderId))
	}

	return c.JSON(resp)
}

func (h *handler) GetA2ABalance(c fiber.Ctx) error {
	balance, err := h.app.GetA2ABalance(c.Context())
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}
	return c.JSON(fiber.Map{"balance": balance})
}

func (h *handler) CancelA2ATransaction(c fiber.Ctx) error {
	var transactionId = util.ParseInt(c.Params("transaction_id"))
	if transactionId == 0 {
		return h.badRequestResponse(c, "transaction_id is incorrect")
	}

	var req model.CancelA2ARequest
	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error()+string(c.Body()))
	}

	err = h.app.CancelA2ATransaction(c.Context(), transactionId, req.Comment)
	if err != nil {
		return h.errorResponse(c, "internal_error", err.Error())
	}

	return c.JSON(fiber.Map{"status": "success"})
}

func (h *handler) UpdateA2ATransaction(c fiber.Ctx) error {
	transactionId := util.ParseInt(c.Params("transaction_id"))

	if transactionId == 0 {
		return h.badRequestResponse(c, "transaction_id is incorrect!")
	}
	var req model.CancelA2ARequest

	err := c.Bind().JSON(&req)

	if err != nil {
		return h.badRequestResponse(c, "couldn't serialize request body: "+err.Error()+string(c.Body()))
	}

	if !req.Amount.Valid {
		return h.badRequestResponse(c, "Amount is not valid")
	}

	err = h.app.UpdateA2ATransaction(c.Context(), transactionId, int(req.Amount.Int64), req.Comment)
	if err != nil {
		return h.serviceErrorResponse(c, "error on updating a2a: "+err.Error())
	}
	return c.JSON(fiber.Map{"status": "success"})
}

func (h *handler) GetA2aTransactions(c fiber.Ctx) error {
	partnerId := util.ParseInt(c.Query("partner_id"))
	orderId := util.ParseInt(c.Query("order_id"))
	fromDate := c.Query("from_date")
	toDate := c.Query("to_date")
	status := util.ParseInt(c.Query("status"))
	page := util.ParseInt(c.Query("page", "1"))
	pageSize := util.ParseInt(c.Query("page_size", "20"))
	download := util.ParseBool(c.Query("download"))

	var filterParams = map[string]any{
		"page":      page,
		"page_size": pageSize,
	}

	if partnerId != 0 {
		filterParams["partner_id"] = partnerId
	}
	if orderId != 0 {
		filterParams["order_id"] = orderId
	}
	if fromDate != "" {
		filterParams["from_date"] = fromDate
	}
	if toDate != "" {
		filterParams["to_date"] = toDate
	}

	if status != 0 {
		filterParams["status"] = status
	}

	resp, err := h.app.GetA2aTransactions(c.Context(), filterParams, download)
	if err != nil {
		return h.errorResponse(c, "internal_error", "error happened: "+err.Error())
	}
	if !download {
		return c.JSON(resp)
	}

	// download excel
	file, err := h.app.CreatePartnerPaymentsReport(resp.Results)
	if err != nil {
		return h.errorResponse(c, "internal_error", "error on creating excel report: %s"+err.Error())
	}

	c.Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Set("Content-Disposition", "attachment;filename=partner_transactions.xlsx")
	return c.SendStream(file)
}
