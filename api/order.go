package api

import (
	"billing_service/util"

	fiber "github.com/gofiber/fiber/v3"
)

func (h *handler) GetOrderTips(c fiber.Ctx) error {
	orderId := 0
	driverId := util.ParseInt(c.Query("driver_id"))
	if driverId == 0 {
		orderId = util.ParseInt(c.Query("order_id"))
		if orderId == 0 {
			return h.badRequestResponse(c, "empty required fileds driver_id and order_id")
		}
	}

	resp, err := h.repo.GetOrderTips(c.Context(), driverId, orderId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(map[string]any{"status": "success", "data": resp})
}
