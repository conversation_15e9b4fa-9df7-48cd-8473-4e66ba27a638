package api

import (
	"net/http"
	"strings"

	"billing_service/util"

	fiber "github.com/gofiber/fiber/v3"
	null "github.com/guregu/null/v6"
)

func (h *handler) GetClientPromocodeInfo(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}

	promocode := strings.TrimSpace(c.Query("promocode"))
	promocode = strings.ToUpper(promocode)
	if promocode == "" {
		return h.badRequestResponse(c, "emty promocode")
	}

	resp, errType, err := h.app.GetClientPromoCodeInfo(c.Context(), clientId, promocode)
	if err != nil {
		if errType != "" {
			return h.errorResponse(c, errType, err.Error())
		}
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.<PERSON>(fiber.Map{"info": resp})
}

func (h *handler) GetPromocodeInfo(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}
	resp, err := h.repo.GetPromocodeInfo(c.Context(), clientId, c.Params("promocode"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	if !resp.Success {
		return c.JSON(fiber.Map{"success": false, "message": resp.Message})
	}

	return c.JSON(resp)
}

func (h *handler) GetPromocodeValidation(c fiber.Ctx) error {
	clientId := util.ParseInt(c.Params("client_id"))
	if clientId == 0 {
		return h.badRequestResponse(c, "incorrect client_id")
	}
	errType, err := h.repo.GetPromocodeValidation(c.Context(), clientId, c.Params("promocode"))
	if err != nil {
		if errType != "" {
			return h.errorResponse(c, errType, err.Error())
		}
		return h.serviceErrorResponse(c, err.Error())
	}
	return c.SendStatus(http.StatusOK)
}

type PromocodeRequest struct {
	Code      string    `json:"code"`
	ClientId  null.Int  `json:"client_id,omitzero"`
	ValidDays null.Int  `json:"valid_days,omitzero"`
	IsActive  null.Bool `json:"is_active,omitzero"`
}

func (h *handler) XpanelAddPromocode(c fiber.Ctx) error {
	var req PromocodeRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	err = h.app.XpanelAddPromocode(c.Context(), req.Code, req.ClientId, req.ValidDays)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.SendStatus(http.StatusOK)
}

func (h *handler) XpanelUpdatePromocode(c fiber.Ctx) error {
	var req PromocodeRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	err = h.repo.XpanelUpdatePromocode(c.Context(), req.Code, req.ClientId, req.IsActive)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.SendStatus(http.StatusOK)
}

func (h *handler) XpanelGetPromocodes(c fiber.Ctx) error {
	var (
		code     = c.Query("code")
		clientId int
		isActive null.Bool
		page     = util.ParseInt(c.Query("page"))
		limit    = util.ParseInt(c.Query("limit"))
	)

	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 20
	}

	cl := c.Query("client_id")
	if cl != "" {
		clientId = util.ParseInt(cl)
	}

	isAct := c.Query("is_active")
	if isAct != "" {
		isActive = null.BoolFrom(util.ParseBool(isAct))
	}

	resp, err := h.app.XpanelGetPromocodes(c.Context(), code, clientId, isActive, page, limit)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}
