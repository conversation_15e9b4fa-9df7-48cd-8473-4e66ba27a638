package model

type DriverBalanceHistory struct {
	Time       int    `json:"time"`
	AmountType string `json:"type_amount"`
	Amount     string `json:"amount"`
	PayType    string `json:"type_pay"`
	OrderId    string `json:"order_id,omitempty"`
	Comment    string `json:"comment,omitempty"`
}

type DriverBalanceHistoryResponseV2 struct {
	Deposit []DriverBalanceHistoryV2 `json:"deposit"`
}

type DriverBalanceHistoryV2 struct {
	Time       string `json:"time"`
	AmountType string `json:"type_amount"`
	Amount     string `json:"amount"`
	Balance    string `json:"balance"`
	Comment    string `json:"comment"`
}

type DriverPromoBalanceToBalanceResponse struct {
	Balance      int `json:"balance"`
	PromoBalance int `json:"promo_balance"`
}

type Response struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
}

type DriverBilling struct {
	ID        int64  `json:"id"`
	Phone     string `json:"phone"`
	FirstName string `json:"name"`
	LastName  string `json:"surename"`
	StartDate string `json:"start_date"`
	CompanyID int    `json:"companyID"`
}

type DriverBalance struct {
	Balance      int    `json:"balance"`
	LastFillDate string `json:"last_fill_date,omitempty"`
}

type DriverPaymentFallback struct {
	Provider string `json:"provider"`
	Link     string `json:"link"`
}

type DriverInfo struct {
	Id    int    `json:"id" db:"id"`
	Name  string `json:"name" db:"name"`
	Phone string `json:"phone" db:"phone"`
}

type DriverBonusEvent struct {
	DriverId    int `json:"driver_id"`
	BonusAmount int `json:"bonus_amount"`
	ChallengeId int `json:"challenge_id"`
	OrderId     int `json:"order_id"`
}

type RefillDriverBalanceRequest struct {
	OrderId  int `json:"order_id"`
	DriverId int `json:"driver_id"`
	Amount   int `json:"amount"`
	Balance  int `json:"balance"`
}

type DriverStatus struct {
	DriverId int  `json:"driver_id"`
	Status   bool `json:"status"`
}
