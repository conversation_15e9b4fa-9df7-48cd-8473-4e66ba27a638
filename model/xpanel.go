package model

import (
	"time"

	null "github.com/guregu/null/v6"
)

type XpanelClientPaymePaymentsResponse struct {
	Status   string               `json:"status"`
	Payments []XpanelPaymePayment `json:"result"`
}

type XpanelPaymePayment struct {
	Id            int               `json:"transaction_id"`
	OrderId       int               `json:"order_id"`
	Amount        int               `json:"amount"`
	PaymentTypeId int               `json:"payment_type_id"`
	TypeId        int               `json:"type_id"`
	Status        int               `json:"status"`
	PaycomStatus  null.Int          `json:"paycom_status"`
	CreatedAt     string            `json:"created_at"`
	Invoice       string            `json:"-"`
	IsP2P         bool              `json:"-"`
	Card          *XpanelClientCard `json:"client_card"`
}

type XpanelClientCard struct {
	Id     int    `json:"card_id"`
	Number string `json:"card_number"`
	Expire string `json:"expiry_date"`
}

type XpanelA2CPaymentsResponse struct {
	Count    int                `json:"count"`
	Payments []XpanelA2CPayment `json:"results"`
}

type XpanelA2CPayment struct {
	OrderId    int         `json:"order_id"`
	DriverId   int         `json:"driver_id"`
	Amount     int         `json:"amount"`
	Status     int         `json:"status"`
	ProviderId int         `json:"provider_type"`
	Invoice    string      `json:"invoice"`
	CreatedAt  string      `json:"created_at"`
	UpdatedAt  string      `json:"updated_at"`
	CardNumber null.String `json:"card_pan"`
}

type XpanelClientDebtsResponse struct {
	Count int                `json:"count" db:"count"`
	Debts []XpanelClientDebt `json:"debts"`
}

type XpanelClientDebt struct {
	Id          int         `json:"id"`
	OrderId     int         `json:"order_id"`
	ClientId    int         `json:"client_id"`
	Amount      int         `json:"amount"`
	Status      int         `json:"status_id"`
	ClientPhone null.String `json:"client_phone"`
	CardHash    null.String `json:"card_hash"`
	CreatedAt   string      `json:"created_at"`
	PaidAt      string      `json:"paid_at,omitempty"`
}

type XpanelClientCardsResponse struct {
	Count int                 `json:"count"`
	Cards []XpanelClientCard2 `json:"cards"`
}

type XpanelClientCard2 struct {
	ClientID      int         `json:"client_id"`
	Number        string      `json:"number"`
	Expire        string      `json:"expiry_date"`
	PaymeCardHash null.String `json:"card_hash"`
	IsDeleted     bool        `json:"is_deleted"`
}

type XpanelGetDriverCardsResponse struct {
	Count int                `json:"count"`
	Cards []XpanelDriverCard `json:"cards"`
}

type XpanelDriverCard struct {
	DriverID  int       `json:"driver_id"`
	Number    string    `json:"number"`
	Expire    string    `json:"expiry_date"`
	CreatedAt time.Time `json:"created_at"`
	DeletedAt null.Time `json:"deleted_at,omitzero"`
	IsDeleted bool      `json:"is_deleted"`
}

type AllBalanceResponse struct {
	ClientsCashbackBalance int `json:"clients_balance"`
	DriversBalance         int `json:"drivers_balance"`
}

type OrderPaymentQueue struct {
	TaskId   int64  `json:"task_id" db:"task_id"`
	OrderId  int    `json:"order_id" db:"order_id"`
	DriverId int    `json:"driver_id" db:"driver_id"`
	Amount   int    `json:"amount" db:"amount"`
	TaskName string `json:"task_name" db:"task_name"`
}

type ClientPaymeTransaction struct {
	Id              int         `json:"id"`
	ReceiptId       string      `db:"receipt_id"`
	StatusId        int         `db:"status_id"`
	Type            int         `db:"type_id"`
	Amount          int         `db:"amount"`
	CardId          int         `db:"card_id"`
	CreatedTime     time.Time   `db:"created_at"`
	PerformTime     null.Time   `db:"performed_at"`
	CancelTime      null.Time   `db:"canceled_at"`
	Reason          null.Int    `db:"reason"`
	ClientCardToken null.String `db:"card_token"`
	DriverCardToken null.String `db:"driver_card_token"`
	DriverCardId    null.Int    `db:"driver_card_id"`
	DriverId        null.Int    `db:"driver_id"`
	OrderId         null.Int    `db:"order_id"`
	ClientId        null.Int    `db:"client_id"`
}

type ClientPaymeCancelledTransaction struct {
	StatusId  int    `db:"status_id"`
	ReceiptId string `db:"paycom_id"`
}

func (t ClientPaymeTransaction) IsCreated() bool {
	return t.StatusId == PaymentStatusCreated && t.ReceiptId != ""
}

func (t ClientPaymeTransaction) IsFinished() bool {
	return t.StatusId == PaymentStatusFinished
}

func (t ClientPaymeTransaction) GetSoumAmount() int {
	return t.Amount / 100
}

type DriverPaymeTransaction struct {
	ReceiptId   string    `db:"receipt_id"`
	StatusId    int       `db:"status_id"`
	DriverId    int       `db:"driver_id"`
	Amount      int       `db:"amount"`
	CreatedTime time.Time `db:"created_at"`
	PerformTime null.Time `db:"performed_at"`
	CancelTime  null.Time `db:"canceled_at"`
	Reason      null.Int  `db:"reason"`
}

func (t DriverPaymeTransaction) GetSoumAmount() int {
	return t.Amount / 100
}

type PaymentXpanel struct {
	Id         int    `json:"id" db:"id"`
	Amount     int    `json:"amount" db:"amount"`
	Status     int    `json:"status_id" db:"status"`
	CardId     int    `json:"card_id" db:"card_id"`
	CardNumber string `json:"card_number" db:"card_number"`
	Invoice    string `json:"invoice" db:"invoice"`
	CreatedAt  string `json:"created_at" db:"timestamp"`
	UpdatedAt  string `json:"updated_at" db:"updated_at"`
}

type BillingPayments struct {
	Count           int              `json:"count"`
	TransactionData []BillingPayment `json:"transactions"`
}

type BillingPayment struct {
	ID                       string                  `json:"id"`
	AccountNumber            string                  `json:"account_number"`
	DestinationAccountNumber null.String             `json:"dst_account_number"`
	Amount                   null.Float              `json:"amount"`
	Operation                BillingPaymentOperation `json:"operation"`
	ReceivedAmount           null.Float              `json:"received_amount"`
	OrderID                  null.Int                `json:"order_id"`
	Type                     string                  `json:"type"`
	Description              null.String             `json:"desc"`
	Month                    time.Time               `json:"month"`
	CreatedAt                time.Time               `json:"created_at"`
}

type BillingPaymentOperation struct {
	Type        int    `json:"type"`
	Alias       string `json:"alias"`
	Description string `json:"description"`
}

type BillingAccounts struct {
	Accounts []BillingAccount `json:"accounts"`
	Total    int64            `json:"total"`
}

type BillingAccount struct {
	ID            int64       `json:"id" db:"id"`
	AccountNumber string      `json:"account_number" db:"account_number"`
	Description   null.String `json:"description" db:"description"`
	Balance       null.Float  `json:"balance" db:"balance"`
}

type BillingAccountBalance struct {
	ID            null.Int    `json:"id"`
	Balance       null.Float  `json:"balance"`
	AccountNumber null.String `json:"account_number"`
}

type WithdrawPaymentRequest struct {
	AccountNumber string `json:"account_number"`
	Amount        int    `json:"amount"`
	OrderID       int    `json:"order_id"`
	// Description   null.String `json:"description"`
}

type XpanelGetBillingPaymentsRequest struct {
	Page          int       `json:"offset" db:"offset"`
	Limit         int       `json:"limit" db:"limit"`
	Search        string    `json:"search" db:"search"`
	AccountNumber string    `json:"account_number" db:"account_number"`
	Type          string    `json:"type" db:"type"`
	OpType        int       `json:"op_type" db:"op_type"`
	OrderID       string    `json:"order_id" db:"order_id"`
	TransactionID string    `json:"transaction_id" db:"transaction_id"`
	From          time.Time `json:"from_date" db:"from_date"`
	To            time.Time `json:"to_date" db:"to_date"`
	MytaxiID      string    `json:"mytaxi_id" db:"mytaxi_id"`
	UserID        int       `json:"user_id" db:"user_id"`
	UserType      string    `json:"user_type" db:"user_type"`
}
