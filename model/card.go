package model

import null "github.com/guregu/null/v6"

type Card struct {
	Id         int         `json:"id"`
	Number     string      `json:"number"`
	Expire     null.String `json:"expire,omitempty"`
	Status     string      `json:"card_status,omitempty"`
	Brand      string      `json:"brand,omitempty"`
	Exists     bool        `json:"exists,omitempty"`
	Category   null.String `json:"-"`
	PaymeToken null.String `json:"-"`
	AtmosToken null.String `json:"-"`
	PaymeHash  null.String `json:"-"`
	AtmosHash  null.String `json:"-"`
	FullNumber null.String `json:"-"`
	CheckedAt  null.Time   `json:"-"`
}

type ClientCardBalance struct {
	Is<PERSON>nough bool `json:"is_enough"`
}

type GetCardTokenResponse struct {
	PaymentId      string `json:"payment_id,omitempty"`
	IdempotencyKey string `json:"idempotency_key,omitempty"`
	PaymentUrl     string `json:"payment_url"`
}

type CheckCardTokenResponse struct {
	Status      string `json:"status"`
	ErrorReason string `json:"error_type,omitempty"`
}
