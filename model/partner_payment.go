package model

import (
	"time"

	"github.com/guregu/null/v6"
)

type A2ATransaction struct {
	OrderId      int     `json:"order_id" db:"order_id"`
	PartnerId    int     `json:"partner_id" db:"partner_id"`
	Amount       int     `json:"amount" db:"amount"`
	OrderAmount  int     `json:"order_amount" db:"total_sum"`
	ProviderType int     `json:"provider_type" db:"provider_type"`
	Commission   float32 `json:"commission" db:"percentage"`
	Status       int     `json:"-" db:"status"`
	Log          string  `json:"-" db:"log"`
}

type A2ATransactionView struct {
	Id           int         `json:"id" db:"id"`
	CreatedAt    time.Time   `json:"created_at" db:"created_at"`
	PartnerId    int         `json:"partner_id" db:"partner_id"`
	OrderId      int         `json:"order_id" db:"order_id"`
	Amount       float64     `json:"amount" db:"amount"`
	Invoice      null.String `json:"invoice" db:"invoice"`
	IdpKey       null.String `json:"idempotency_key" db:"idempotency_key"`
	Status       int         `json:"status" db:"status"`
	Reason       int         `json:"reason" db:"reason"`
	ProviderType int         `json:"provider_type" db:"provider_type"`
	Comment      null.String `json:"comment" db:"comment"`
	Percentage   null.Float  `json:"percentage" db:"percentage"`
	TotalSum     null.Int    `json:"total_sum" db:"total_sum"`
	TransferId   null.Int    `json:"transfer_id" db:"transfer_id"`
}

type CancelA2ARequest struct {
	Comment null.String `json:"comment"`
	Amount  null.Int    `json:"amount"`
}

type A2AList struct {
	Count   int                  `json:"count"`
	Results []A2ATransactionView `json:"results"`
}

type A2AlistReqParams struct {
	PartnerId int    `json:"partner_id"`
	OrderId   int    `json:"order_id"`
	FromDate  string `json:"from_date"`
	ToDate    string `json:"to_date"`
	Status    int    `json:"status"`
	Page      int    `json:"page"`
	PageSize  int    `json:"page_size"`
}

type A2APartnerTransfer struct {
	Id             int         `json:"id" db:"id"`
	PartnerId      int         `json:"partner_id" db:"partner_id"`
	Amount         float64     `json:"amount" db:"amount"`
	Status         int         `json:"status" db:"status"`
	Invoice        null.String `json:"invoice" db:"invoice"`
	IdempotencyKey null.String `json:"idempotency_key" db:"idempotency_key"`
	ProviderType   int         `json:"provider_type" db:"provider_type"`
}
