package model

import null "github.com/guregu/null/v6"

type Partner struct {
	Id          int      `db:"id"`
	AutoPayment bool     `db:"auto_payment"`
	DelayPeriod null.Int `db:"delay_period"`
}

type PartnerResponse struct {
	ID      int     `json:"id"`
	Name    string  `json:"name"`
	INN     int     `json:"inn"`
	Percent float64 `json:"percent"`
	Status  int     `json:"status"`
}

type A2APartner struct {
	Id          int         `db:"id"`
	Name        string      `db:"name"`
	AutoPayment bool        `db:"auto_payment"`
	DelayPeriod null.Int    `db:"delay_period"`
	TIN         string      `db:"inn"`
	Account     string      `db:"account"`
	BankCode    null.String `db:"bank_code"`
	A2AAllowed  null.String `db:"a2a_queue_control"`
}
