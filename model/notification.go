package model

const MessageToOperClientDebt = "Поездка закончена с долгом!\n\nЗаказ: %d\nCтоимость: %d сум\nКлиент: %d\nВодитель: %d"
const MessageToOperSuspiciousOrder = "Подозрительный заказ!\n\nЗаказ: %d\nCтоимость: %d сум\nКлиент: %d\nВодитель: %d\n\n@call_center - после проверки, необходимо вручную перевести на карту водителя"

var MessageSmsCode = map[string]string{
	"ru": "Проверочный код для перевода средств: %s",
	"en": "Your verification code for money transfer is: %s",
	"uz": "Pul o'tkazmasining tasdiqlash kodi: %s",
}

type TelegramNotification struct {
	Channel string `json:"channel_type"`
	Message string `json:"message"`
}

type PushNotification struct {
	ClientId int    `json:"client_id,omitempty"`
	DriverId int    `json:"driver_id,omitempty"`
	OrderId  int    `json:"order_id,omitempty"`
	Amount   int    `json:"amount,omitempty"`
	Message  string `json:"message"`
	// Title     string    `json:"title,omitempty"`
	// Type      string    `json:"type,omitempty"`
	// ImageURL  string    `json:"image_url,omitempty"`
	// TTL       int       `json:"ttl,omitempty"`
	// CreatedAt time.Time `json:"created_at,omitempty"`
}

type DriverSocketNotification struct {
	DriverId int                          `json:"driver_id"`
	Body     DriverSocketNotificationBody `json:"push_data"`
}

type DriverSocketNotificationBody struct {
	MsgType string `json:"mess_type"`
	OrderId int    `json:"order_id"`
	Amount  int    `json:"tip_amount"`
}

type SMS struct {
	SmsNumber string `json:"sms_number"`
	Message   string `json:"mess_body"`
}
